{"name": "hs-cypress-tests", "title": "Help Scout", "private": true, "author": "Help Scout", "homepage": "http://www.helpscout.net/", "dependencies": {"@testing-library/cypress": "^8.0.2", "cypress": "^12.6.0", "cypress-cloud": "^1.9.3", "cypress-debugger": "^1.0.5", "cypress-real-events": "^1.7.6", "faker": "4.1.0", "uuid": "3.3.2"}, "scripts": {"cy:run": "cypress run", "cy:open": "cypress open", "cy:open:local": "cypress open --env ENV=local", "cy:open:ryan": "cypress open --config baseUrl='https://hs-app.ryan.stack.private.hsenv.io/'", "cy:open:betina": "cypress open --config baseUrl='https://hs-app.betina.stack.private.hsenv.io/'", "cy:open:wdp": "cypress open --config baseUrl='https://webdev-ci-secure.helpscout.net/'", "cy:open:prod": "cypress open --env ENV=prod --config baseUrl='https://secure.helpscout.net/'", "cy:open:nonprod": "cypress open --config baseUrl='https://hsapp.nonprod.superscout.net/'", "cy:run:nonprod": "cypress run -- --env TAG=smoke --config baseUrl='https://hsapp.nonprod.superscout.net'", "cy:regression": "cypress run --env TAG=regression", "cy:regression-wdp": "cypress run --env ENV=wdp,TAG=regression-wdp", "cy:smoke": "cypress run --env ENV=prod,TAG=smoke", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "devDependencies": {"eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-prettier": "^4.2.1", "prettier": "2.8.0"}}