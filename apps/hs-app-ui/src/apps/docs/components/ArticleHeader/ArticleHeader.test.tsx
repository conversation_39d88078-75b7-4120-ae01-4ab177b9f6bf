import { MemoryRouter, Route } from 'react-router'

import { render, screen } from '@testing-library/react'

import { TEST_IDS, TEXT } from 'apps/docs/constants'
import { PATHS } from 'apps/docs/constants'

import ArticleHeader, { ArticleHeaderProps } from './ArticleHeader'
import { ProviderWrapper } from 'apps/docs/testUtils/components/ProviderWrapper'

describe('ArticleHeader', () => {
  const COLLECTION_ID = 1
  const ARTICLE_ID = 2
  const baseUrl = `/docs/${COLLECTION_ID}/article/${ARTICLE_ID}`
  const articleListURL = `/docs/${COLLECTION_ID}`
  const collection = { id: '2', name: 'My Collection', siteId: '1' }
  const site = { id: '1', name: 'My Support Site' }
  const siteCollectionsForPublicSite = [
    {
      ...site,
      hasPublicSite: true,
      collections: [
        {
          ...collection,
          isCurrent: true, // An article must be in a collection so we don't need to test this for false.
        },
      ],
    },
  ]
  const siteCollectionsForPrivateSite = [
    {
      ...site,
      hasPublicSite: false,
      collections: [
        {
          ...collection,
          isCurrent: true,
        },
      ],
    },
  ]

  const renderHeader = (props?: Partial<ArticleHeaderProps>) => {
    const baseProps = {
      className: '',
      publishArticle: jest.fn(),
      unpublishArticle: jest.fn(),
      updateArticle: jest.fn(),
      discardDraft: jest.fn(),
      previewLink: '',
      articleStatus: undefined,
      isPublishingOrUpdating: false,
      isArticleUnsaved: false,
    }
    const headerProps = { ...baseProps, ...(props || {}) }

    return render(
      <ProviderWrapper>
        <MemoryRouter initialEntries={[baseUrl]}>
          <Route path={`${PATHS.BASENAME}${PATHS.ARTICLE_COMPOSE}`}>
            <ArticleHeader {...headerProps}></ArticleHeader>
          </Route>
        </MemoryRouter>
      </ProviderWrapper>
    )
  }

  describe('Navigation Control Group', () => {
    it('should have a navigation control group', () => {
      const { getByTestId } = renderHeader()

      const navControlHeader = getByTestId('navigation-control-group')
      expect(navControlHeader).toBeInTheDocument()
    })

    it('should be configured to navigate back to the Article list', () => {
      const { getByTestId } = renderHeader()

      const backLink = getByTestId('back-button-link')
      expect(backLink).toHaveAttribute('href', `${articleListURL}`)
    })
  })

  describe('Preview Article', () => {
    const articlePreviewUrl =
      'https://my-super-cool-support-site.helpscoutdocs.com/article/21-test-article?auth=true'

    it('should open the article preview in a new tab', () => {
      const { getByRole } = renderHeader({
        previewLink: articlePreviewUrl,
        siteCollections: siteCollectionsForPublicSite,
      })

      const previewButton = getByRole('link', { name: 'Preview' })
      expect(previewButton).toHaveAttribute('target', '_blank')
    })

    it('should navigate to the article preview', () => {
      const { getByRole } = renderHeader({
        previewLink: articlePreviewUrl,
        siteCollections: siteCollectionsForPublicSite,
      })

      const previewButton = getByRole('link', { name: 'Preview' })
      expect(previewButton).toHaveAttribute('href', articlePreviewUrl)
    })

    it('should not render the preview button if the site is private', () => {
      const { queryByRole } = renderHeader({
        previewLink: articlePreviewUrl,
        siteCollections: siteCollectionsForPrivateSite,
      })

      const previewButton = queryByRole('link', { name: 'Preview' })
      expect(previewButton).not.toBeInTheDocument()
    })

    it('should not render the preview button if the site collection data is missing', () => {
      // The data would be missing if the site is still loading
      const { queryByRole } = renderHeader({
        previewLink: articlePreviewUrl,
      })

      const previewButton = queryByRole('link', { name: 'Preview' })
      expect(previewButton).not.toBeInTheDocument()
    })
  })

  describe('Publish/Update Article Button', () => {
    it('should show a loading state when loading the page', () => {
      renderHeader({ articleStatus: undefined })

      expect(
        screen.getByTestId(TEST_IDS.ARTICLE_LIFECYCLE.LOADER)
      ).toBeVisible()
    })

    it('should show a Publish button if the article is not published yet', () => {
      renderHeader({ articleStatus: 'notpublished' })

      const updateButton = screen.getByText(TEXT.BUTTON.PUBLISH)
      expect(updateButton).toBeVisible()
    })

    it('should show an Update button if the article has been published', () => {
      renderHeader({ articleStatus: 'published' })

      const updateButton = screen.getByText(TEXT.BUTTON.UPDATE)
      expect(updateButton).toBeVisible()
    })

    it('should show button with a not-enabled class if the article has not been modified', () => {
      renderHeader({ articleStatus: 'published' })

      // TODO: This test id should ideally be TEST_IDS.ARTICLE_LIFECYCLE.PUBLISH_UPDATE (passed from the props at derivePublishButtonState)
      // HSDS keeps on passing DropList.SplitButtonAction as the test id, this needs to be debugged
      const button = screen.getByTestId('DropList.SplitButtonAction')

      expect(button).toHaveClass('not-enabled')
    })
  })
})
