import { MemoryRouter, Route } from 'react-router'

import { fireEvent, render } from '@testing-library/react'

import { TEST_IDS } from 'apps/docs/constants'
import { PATHS } from 'apps/docs/constants'

import { PublishButton, PublishButtonProps } from './PublishButton'

describe('PublishButton', () => {
  const COLLECTION_ID = 1
  const ARTICLE_ID = 2
  const baseUrl = `/docs/${COLLECTION_ID}/article/${ARTICLE_ID}`

  const renderPublishActionsMenu = (
    props: Partial<PublishButtonProps> = {}
  ) => {
    const baseProps = {
      articleStatus: 'notpublished',
      isArticleUnsaved: true,
      isPublishingOrUpdating: false,
      publishArticle: jest.fn(),
      unpublishArticle: jest.fn(),
      discardDraft: jest.fn(),
    } as PublishButtonProps
    const publishActionMenuProps = { ...baseProps, ...props }

    return render(
      <MemoryRouter initialEntries={[baseUrl]}>
        <Route path={`${PATHS.BASENAME}${PATHS.ARTICLE_COMPOSE}`}>
          <PublishButton {...publishActionMenuProps}></PublishButton>
        </Route>
      </MemoryRouter>
    )
  }

  describe('Publish Action Menu', () => {
    it('should have a publish action menu', () => {
      const { getByTestId } = renderPublishActionsMenu()

      const publishActionsMenu = getByTestId(
        TEST_IDS.ARTICLE_LIFECYCLE.PUBLISH_UPDATE
      )
      expect(publishActionsMenu).toBeInTheDocument()
    })
  })

  describe('Menu contents', () => {
    it('move menu item should not be be present when there are no site collections', () => {
      const { queryByText, getByTestId } = renderPublishActionsMenu()

      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(queryByText('Move')).toBeNull()
    })

    it('move menu item should not be present when there are no "other" site collections', () => {
      const siteCollections = [
        { id: '1', name: 'Site 1', hasPublicSite: true, collections: [] },
        {
          id: '2',
          name: 'Site 2',
          hasPublicSite: true,
          collections: [
            { id: '1', name: 'Collection 1', siteId: '2', isCurrent: true },
          ],
        },
      ]
      const { queryByText, getByTestId } = renderPublishActionsMenu({
        siteCollections,
      })
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(queryByText('Move')).toBeNull()
    })

    it('move menu item should be present when there are only "other" site collections', () => {
      const siteCollections = [
        { id: '1', name: 'Site 1', hasPublicSite: true, collections: [] },
        {
          id: '2',
          name: 'Site 2',
          hasPublicSite: true,
          collections: [
            { id: '1', name: 'Collection 1', siteId: '2', isCurrent: false },
          ],
        },
      ]
      const { getByText, getByTestId } = renderPublishActionsMenu({
        siteCollections,
      })
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(getByText('Move')).toBeInTheDocument()
    })

    it('move menu item should be present when there are "other" site collections', () => {
      const siteCollections = [
        { id: '1', name: 'Site 1', hasPublicSite: true, collections: [] },
        {
          id: '2',
          name: 'Site 2',
          hasPublicSite: true,
          collections: [
            { id: '1', name: 'Collection 1', siteId: '2', isCurrent: true },
            { id: '2', name: 'Collection 2', siteId: '2', isCurrent: false },
          ],
        },
      ]
      const { getByText, getByTestId } = renderPublishActionsMenu({
        siteCollections,
      })
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(getByText('Move')).toBeInTheDocument()
    })

    it('unpublish menu item should be present in an unpublished article', () => {
      const { getByText, getByTestId } = renderPublishActionsMenu({
        articleStatus: 'published',
      })
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(getByText('Unpublish')).toBeInTheDocument()
    })

    it('discard menu item should be present in an unsaved and published article', () => {
      const { getByText, getByTestId } = renderPublishActionsMenu({
        articleStatus: 'published',
      })
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(getByText('Discard changes')).toBeInTheDocument()
    })

    it('discard menu item should be present in an unsaved an unpublished article', () => {
      const { getByText, getByTestId } = renderPublishActionsMenu()
      const togglerButton = getByTestId('DropList.SplitButtonToggler')
      fireEvent.click(togglerButton)
      expect(getByText('Discard changes')).toBeInTheDocument()
    })
  })
})
