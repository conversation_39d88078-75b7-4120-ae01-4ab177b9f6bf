import { useCanPreviewSite } from './hooks/useCanPreviewSite'

import { PublishingControlsUI } from './PublishingControls.css'

import { PreviewButton, PublishButton, SaveIndicator } from './components'
import type {
  PreviewButtonProps,
  PublishButtonProps,
  SaveIndicatorProps,
} from './components'

export type PublishingControlsProps = PreviewButtonProps &
  PublishButtonProps &
  SaveIndicatorProps

export function PublishingControls({
  previewLink,
  savedState,
  siteCollections,
  ...rest
}: PublishingControlsProps) {
  const canPreviewSite = useCanPreviewSite(siteCollections)

  return (
    <PublishingControlsUI data-testid="publishing-controls">
      <SaveIndicator savedState={savedState} />
      {canPreviewSite && <PreviewButton previewLink={previewLink} />}
      <PublishButton siteCollections={siteCollections} {...rest} />
    </PublishingControlsUI>
  )
}
