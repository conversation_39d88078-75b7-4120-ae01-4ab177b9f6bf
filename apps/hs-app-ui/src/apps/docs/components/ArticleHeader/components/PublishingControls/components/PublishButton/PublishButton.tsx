import classNames from 'classnames'
import { SyntheticEvent, useMemo, useRef, useState } from 'react'

import {
  derivePublishButtonState,
  derivePublishMenuItems,
  getArticleLifecycleStatus,
  meetsMoveRequirements,
  siteCollectionsToMenuItems,
} from './utils'
import type { MenuItem } from './utils'

import { useNoty } from 'shared/hooks'

import { MENU_ITEM_ACTIONS, TEXT } from 'apps/docs/constants'
import { NOTY_TIMEOUT, TEST_IDS } from 'apps/docs/constants'

import { PublishActionsMenuUI, RightIconUI } from './PublishButton.css'

import type { SiteCollection } from 'apps/docs/schema'
import type { ArticleStatus } from 'apps/docs/schema'
import DropList, { SplittedButton } from 'hsds/components/drop-list'
import {
  ListItemIconUI,
  ListItemUI,
} from 'hsds/components/drop-list/DropList.styles'
import Icon from 'hsds/components/icon'
import Spinner from 'hsds/components/spinner'
import Checkmark from 'hsds/icons/check'
import ArrowLeft from 'hsds/icons/chevron-left-tiny'
import ArrowRight from 'hsds/icons/chevron-right-tiny'

const MENU_TYPE = {
  COLLECTIONS_MENU: 'collections',
  PUBLISH_MENU: 'publishActions',
} as const

type MenuType = (typeof MENU_TYPE)[keyof typeof MENU_TYPE]

const MIN_ITEMS_TO_SHOW_SEARCH = 16 // 15 + 1 for the "Back" item.

export type PublishButtonProps = {
  articleStatus?: ArticleStatus

  siteCollections?: SiteCollection[]

  // The function to call when the user clicks the discard button
  discardDraft: () => void

  // Whether the article is unsaved
  isArticleUnsaved: boolean

  // Whether the article is being published or updated
  isPublishingOrUpdating: boolean

  // Whether the menu should be disabled
  isPublishActionsMenuDisabled?: boolean

  // The function to call when the user clicks a collection.
  moveArticle?: (options: {
    destinationCollection: { id: string; name: string }
    sourceCollection: { id: string; name: string }
  }) => void

  // The function to call when the user clicks the publish button
  publishArticle: () => void

  // The function to call when the user clicks the unpublish button
  unpublishArticle: () => void
}

export function PublishButton({
  articleStatus,
  discardDraft,
  isArticleUnsaved,
  isPublishActionsMenuDisabled: isDisabled = false,
  isPublishingOrUpdating,
  moveArticle = () => undefined,
  publishArticle,
  siteCollections = [],
  unpublishArticle,
}: PublishButtonProps) {
  const togglerRef = useRef<HTMLButtonElement>()
  const { showMessageNoty } = useNoty()

  const canMoveArticle = meetsMoveRequirements(siteCollections)

  const sourceCollection = useMemo(() => {
    const defaultValue = { id: '', name: 'Current Collection' }
    if (!canMoveArticle || !siteCollections) return defaultValue

    const all = siteCollections.reduce<SiteCollection['collections']>(
      (previousValue, currentValue) => [
        ...previousValue,
        ...currentValue.collections,
      ],
      []
    )

    return all.find(({ isCurrent }) => isCurrent) ?? defaultValue
  }, [siteCollections, canMoveArticle])

  const articleLifecycleStatus = getArticleLifecycleStatus({
    isPublishingOrUpdating,
    isArticleUnsaved,
    articleStatus,
  })
  const publishMenuItems: MenuItem[] = derivePublishMenuItems(
    articleLifecycleStatus,
    canMoveArticle
  )
  const siteCollectionsMenuItems: MenuItem[] =
    siteCollectionsToMenuItems(siteCollections)

  const menuStatus = derivePublishButtonState(
    publishArticle,
    showUnmodifiedArticleNotice,
    articleLifecycleStatus
  )

  const [menuType, setMenuType] = useState<MenuType>(MENU_TYPE.PUBLISH_MENU)
  const [menuOpen, setMenuOpen] = useState(false)

  /**
   * Shows a message noty when a user tries to publish an unmodified article
   */
  function showUnmodifiedArticleNotice() {
    showMessageNoty(TEXT.MESSAGE_NOTY.ARTICLE_UP_TO_DATE, {
      icon: Checkmark,
      timeout: NOTY_TIMEOUT,
    })
  }

  /**
   * Handles the menu open/close state change.
   * @param isOpen  Whether the menu is open.
   */
  function handleOnOpenedStateChange(isOpen: boolean) {
    if (isOpen) {
      setMenuOpen(true)
    } else {
      // Reset menu to actions on close so that the menu is not stuck in the
      // state it was in when the menu was closed.
      // Note: This does not get fired on when an item is selected so we also
      // handle this case in handleSelect.
      // @ts-ignore
      setMenuType(MENU_TYPE.PUBLISH_MENU)
      setMenuOpen(false)
    }
  }

  /**
   * Handles the click event for an item in the publish button split menu.
   * @param _             The event is ignored.
   * @param selectedItem  The item that was clicked.
   */
  function handleSelect(_: SyntheticEvent, selectedItem: MenuItem) {
    const {
      action,
      id: collectionId,
      isCurrent,
      siteId,
      type,
      value,
    } = selectedItem

    if (type === 'action' && action === MENU_ITEM_ACTIONS.PUBLISH_MENU.BACK) {
      return setMenuType(MENU_TYPE.PUBLISH_MENU)
    }

    if (type === 'action' && action === MENU_ITEM_ACTIONS.PUBLISH_MENU.MOVE) {
      // Change menu and exit early so menu stays open
      // @ts-ignore
      return setMenuType(MENU_TYPE.COLLECTIONS_MENU)
    }

    // Reset menu and close it.
    // @ts-ignore
    setMenuType(MENU_TYPE.PUBLISH_MENU)
    setMenuOpen(false)

    // Perform any actions that are needed for the selected item.
    if (
      type === 'action' &&
      action === MENU_ITEM_ACTIONS.PUBLISH_MENU.DISCARD
    ) {
      discardDraft()
    } else if (
      type === 'action' &&
      action === MENU_ITEM_ACTIONS.PUBLISH_MENU.UNPUBLISH
    ) {
      unpublishArticle()
    } else if (type === 'item' && collectionId && siteId && !isCurrent) {
      moveArticle({
        destinationCollection: { id: collectionId, name: String(value) },
        sourceCollection,
      })
    }
  }

  return (
    <PublishActionsMenuUI>
      <DropList
        animateOptions={{ exit: false }}
        aria-label={menuStatus.text}
        autoSetComboboxAt={MIN_ITEMS_TO_SHOW_SEARCH}
        clearOnSelect
        closeOnSelection={false}
        focusTogglerOnMenuClose={false}
        isMenuOpen={menuOpen}
        items={
          menuType === MENU_TYPE.PUBLISH_MENU
            ? publishMenuItems
            : siteCollectionsMenuItems
        }
        onOpenedStateChange={handleOnOpenedStateChange}
        onSelect={handleSelect}
        renderCustomListItem={({
          isDisabled,
          isHighlighted,
          isSelected,
          item,
        }: {
          isDisabled: boolean
          isHighlighted: boolean
          isSelected: boolean
          item: MenuItem
        }) => (
          <ListItemUI
            as="div"
            className={classNames(
              'DropListItem',
              isDisabled && 'is-disabled',
              isHighlighted && 'is-highlighted',
              isSelected && 'is-selected',
              item.className
            )}
          >
            {item.icon && <ListItemIconUI size="24" icon={item.icon} />}
            {item.className === 'backAction' && (
              <Icon size="24" icon={ArrowLeft} />
            )}
            <span className="item-label">{item.label}</span>
            {item.action === MENU_ITEM_ACTIONS.PUBLISH_MENU.MOVE &&
              item.className !== 'backAction' && (
                <RightIconUI icon={ArrowRight} size="24" />
              )}
            {item.type === 'item' && item.isCurrent && (
              <RightIconUI icon={Checkmark} size="24" />
            )}
          </ListItemUI>
        )}
        toggler={
          <SplittedButton
            actionButtonProps={menuStatus.actionButtonProps}
            aria-label={menuStatus.text}
            data-cy={TEST_IDS.ARTICLE_LIFECYCLE.PUBLISH_UPDATE}
            data-testid={TEST_IDS.ARTICLE_LIFECYCLE.PUBLISH_UPDATE}
            disabled={isDisabled}
            onActionClick={menuStatus.action}
            ref={togglerRef}
            text={
              menuStatus.text || (
                <Spinner
                  data-cy={TEST_IDS.ARTICLE_LIFECYCLE.LOADER}
                  data-testid={TEST_IDS.ARTICLE_LIFECYCLE.LOADER}
                  size={20}
                />
              )
            }
            togglerButtonProps={{
              className: 'publishToggler',
              disabled: isDisabled,
              size: 'sm',
            }}
          />
        }
      />
    </PublishActionsMenuUI>
  )
}
