import classNames from 'classnames'

import { MENU_ITEM_ACTIONS } from 'apps/docs/constants'

import type { SiteCollection } from 'apps/docs/schema'
import { IconType } from 'hsds/icons/baseIcon'

// Represent a menu item, which may be one of:
// - A divider
// - A site group
// - A collection
// - An action (i.e. unpublish, move, back, etc.)
export interface MenuItem {
  // The action to perform when item is clicked
  action?: string

  // The class name to apply to the item
  className: string

  // The id of the item (i.e. site id or collection id)
  id?: string

  // Whether the item is the current item (i.e. the current collection)
  isCurrent?: boolean

  // The sub-menu items (i.e. collections)
  items?: MenuItem[]

  // The text to display for the item
  label?: string

  // The id of the site
  siteId?: string

  // The type of the item
  type: string

  // The value of the item
  value?: string

  icon?: IconType
}

/**
 * Maps site collections to menu items.
 *
 * @param siteCollections
 */
export function siteCollectionsToMenuItems(siteCollections: SiteCollection[]) {
  let siteCollectionsMenuItems: MenuItem[] = siteCollections
    .filter(site => site.collections.length > 0)
    .map(siteWithCollections => ({
      className: '',
      id: siteWithCollections.id,
      items: siteWithCollections.collections.map(collection => ({
        className: classNames(
          'collectionItem',
          collection.isCurrent && 'collectionItem--isCurrent'
        ),
        id: collection.id,
        isCurrent: collection.isCurrent,
        label: collection.name,
        siteId: collection.siteId,
        type: 'item',
        value: collection.name,
      })),
      label: siteWithCollections.name,
      type: 'group',
    }))
  siteCollectionsMenuItems = siteCollectionsMenuItems.length
    ? [
        {
          action: MENU_ITEM_ACTIONS.PUBLISH_MENU.BACK,
          className: 'backAction',
          label: 'Back',
          type: 'action',
        },
        {
          className: '',
          type: 'divider',
        },
        ...siteCollectionsMenuItems,
      ]
    : []
  return siteCollectionsMenuItems
}
