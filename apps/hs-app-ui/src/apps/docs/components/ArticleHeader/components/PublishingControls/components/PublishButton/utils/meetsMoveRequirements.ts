import type { SiteCollection } from 'apps/docs/schema'

/**
 * Returns true if the site collections meet the requirements for moving an
 * article to another collection.
 *
 * @param siteCollections An array of sites with collections.
 */
export function meetsMoveRequirements(siteCollections: SiteCollection[]) {
  if (siteCollections.length < 1) return false

  // If there are more than one site with collections, than there must be
  // at least one collection other than the current collection.
  const sitesWithCollections = siteCollections.filter(
    ({ collections }) => collections.length > 0
  )
  if (sitesWithCollections.length > 1) return true

  // If there is only one site, check that there is at least one more
  // collection than the current collection.
  // The component that uses this component can thereby decide whether to
  // include the current collection or not.
  const onlySite = sitesWithCollections[0]
  return onlySite.collections.filter(({ isCurrent }) => !isCurrent).length > 0
}
