import { useMemo } from 'react'

import type { SiteCollection } from 'apps/docs/schema'

export function useCanPreviewSite(siteCollections?: SiteCollection[]) {
  return useMemo(() => {
    const site = siteCollections?.find(
      ({ collections }) =>
        collections.length > 0 && collections.some(({ isCurrent }) => isCurrent)
    )
    return Boolean(site?.hasPublicSite)
  }, [siteCollections])
}
