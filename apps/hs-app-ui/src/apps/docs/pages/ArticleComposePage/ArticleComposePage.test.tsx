import nock from 'nock'

import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { AUTOSAVE_WAIT_TIME, TEXT } from 'apps/docs/constants'

import { ArticleComposePage } from 'apps/docs/pages'
import { anArticle } from 'apps/docs/testUtils/builders'
import { AppComponentRenderer } from 'apps/docs/testUtils/components'
import { resolveMocks } from 'apps/docs/testUtils/mocks'
import {
  docsApiUrlBuilder,
  docsUrlBuilderWithBaseName,
} from 'apps/docs/testUtils/urlBuilders'
import { waitTime } from 'shared/testUtils/async-utils'

describe('ArticleComposePage', () => {
  // Block realtime features from interfering with these tests by removing
  // the pusher namespace. Without a namespace, the connection to the article's
  // presence channel will not be made and the "subscribing" state will be
  // bypassed.
  const { namespace } = window.hsGlobal.pusher
  beforeAll(() => (window.hsGlobal.pusher.namespace = ''))
  afterAll(() => (window.hsGlobal.pusher.namespace = namespace))

  //* Shared variables
  const article = anArticle()
    .withCollectionId('2')
    .withName('My super awesome article')
    .withNumber(1)
    .withPublicUrl(
      'https://my.fakehelpscout.domain/article/1-my-super-awesome-article'
    )
    .withSlug('my-super-awesome-article')
    .withText(
      '<p>Some cool content</p><p>Another paragraph of cool content</p>'
    )
    .build()

  const baseUrl = 'http://localhost'
  const articleUrl = `${docsApiUrlBuilder.article(article)}?draft=true`
  const collectionsUrl = `${docsApiUrlBuilder.collections()}?page=1`
  const sitesUrl = `${docsApiUrlBuilder.sites()}?page=1`
  const draftsUrl = `${docsApiUrlBuilder.articleDraft(article)}?reload=true`

  const defaultCollectionsResponse = {
    collections: {
      count: 1,
      items: [
        {
          id: '2',
          name: 'My Collection',
          siteId: '1',
          isCurrent: true,
        },
      ],
      page: 1,
      pages: 1,
    },
  }
  const defaultSitesResponse = {
    sites: {
      count: 1,
      items: [
        {
          id: '1',
          name: 'My Site',
          hasPublicSite: true,
        },
      ],
      page: 1,
      pages: 1,
    },
  }

  //* Tests
  describe('Basic Layouts', () => {
    beforeEach(() => {
      nock(baseUrl).get(collectionsUrl).reply(200, defaultCollectionsResponse)
      nock(baseUrl).get(sitesUrl).reply(200, defaultSitesResponse)
    })

    afterEach(nock.cleanAll)

    it('should return an error layout when the article request fails', async () => {
      nock(baseUrl).get(articleUrl).reply(500)
      const { findByText } = renderEditView()
      await resolveMocks()

      const errorMessage = await findByText(TEXT.ERROR_MESSAGE.FETCH_ARTICLE)
      expect(errorMessage).toBeInTheDocument()
    })

    it('should return a success layout when the article request succeeds', async () => {
      nock(baseUrl).get(articleUrl).reply(200, {
        article,
      })
      const { findByDisplayValue } = renderEditView()
      await resolveMocks()

      const articleTitle = await findByDisplayValue(article.name)
      expect(articleTitle).toBeInTheDocument()
    })
  })

  describe('Success Layouts', () => {
    beforeEach(() => {
      nock(baseUrl).get(collectionsUrl).reply(200, defaultCollectionsResponse)
      nock(baseUrl).get(sitesUrl).reply(200, defaultSitesResponse)
    })

    afterEach(nock.cleanAll)

    it('should generate a preview URL for the article when the article is not published', async () => {
      const unpublishedArticle = anArticle()
        .from(article)
        .withHasDraft(false)
        .withStatus('notpublished')
        .build()
      nock(baseUrl).get(articleUrl).reply(200, {
        article: unpublishedArticle,
      })
      const { getByRole } = renderEditView()
      await resolveMocks()

      await waitFor(() =>
        expect(
          getByRole('link', { name: TEXT.BUTTON.PREVIEW })
        ).toHaveAttribute(
          'href',
          `${unpublishedArticle.publicUrl}?auth=true&preview=${unpublishedArticle.id}`
        )
      )
    })

    it('should generate a preview URL for the article when the article is in a draft state', async () => {
      const draftArticle = anArticle()
        .from(article)
        .withHasDraft(true)
        .withStatus('published')
        .build()
      nock(baseUrl).get(articleUrl).reply(200, {
        article: draftArticle,
      })
      const { getByRole } = renderEditView()
      await resolveMocks()

      await waitFor(() =>
        expect(
          getByRole('link', { name: TEXT.BUTTON.PREVIEW })
        ).toHaveAttribute(
          'href',
          `${draftArticle.publicUrl}?auth=true&preview=${draftArticle.id}`
        )
      )
    })

    it('should generate a preview URL for the article when the article is published', async () => {
      const publishedArticle = anArticle()
        .from(article)
        .withHasDraft(false)
        .withStatus('published')
        .build()
      nock(baseUrl).get(articleUrl).reply(200, {
        article: publishedArticle,
      })
      const { getByRole } = renderEditView()
      await resolveMocks()

      await waitFor(() =>
        expect(
          getByRole('link', { name: TEXT.BUTTON.PREVIEW })
        ).toHaveAttribute('href', `${publishedArticle.publicUrl}?auth=true`)
      )
    })
  })

  describe('AI Assist Plugin', () => {
    beforeEach(() => {
      nock(baseUrl).get(collectionsUrl).reply(200, defaultCollectionsResponse)
      nock(baseUrl).get(sitesUrl).reply(200, defaultSitesResponse)
      nock(baseUrl).get(articleUrl).reply(200, { article })
    })

    afterEach(nock.cleanAll)

    it.each`
      enabled  | visibility       | assertion
      ${false} | ${'not visible'} | ${(matcher: HTMLElement) => expect(matcher).toBeNull()}
      ${true}  | ${'visible'}     | ${(matcher: HTMLElement) => expect(matcher).toBeInTheDocument()}
    `(
      'is $visibility when feature flag is $enabled',
      async ({ enabled, assertion }) => {
        window.hsGlobal.features = {
          ...window.hsGlobal.features,
          isAiTextExpansionEnabled: enabled,
        }

        renderEditView()
        await resolveMocks()

        const editor = await screen.findByLabelText('Document Editor')
        userEvent.dblClick(editor)

        const button = screen.queryByLabelText('AI Assist', {
          selector: 'button',
        })
        assertion(button)
      }
    )
  })

  describe('Edit content', () => {
    beforeEach(() => {
      nock(baseUrl).get(collectionsUrl).reply(200, defaultCollectionsResponse)
      nock(baseUrl).get(sitesUrl).reply(200, defaultSitesResponse)
    })

    afterEach(nock.cleanAll)

    it('should display changes after the user types in some changes', async () => {
      nock(baseUrl).get(articleUrl).reply(200, {
        article,
      })

      const { findByText, findByTestId, queryByText } = renderEditView()
      await resolveMocks()

      const articleContent = (await findByTestId(
        'DocsEditor'
      )) as HTMLTextAreaElement
      expect(articleContent.value).toContain('Some cool content')

      expect(queryByText('Saved')).not.toBeInTheDocument()

      nock(baseUrl).put(draftsUrl).reply(200, { article })

      userEvent.click(articleContent)

      // We are using paste here instead of type because typing it makes to content to be one paragraph for each letter.
      userEvent.paste(articleContent, 'adding content')

      await waitTime(AUTOSAVE_WAIT_TIME)
      await waitFor(async () => {
        const savedText = await findByText('Saved')
        expect(savedText).toBeInTheDocument()
        expect(articleContent.value).toContain('adding content')
      })
    })
  })

  //* Helpers
  function renderEditView() {
    return render(
      <AppComponentRenderer
        url={docsUrlBuilderWithBaseName.articleComposeUrl(article)}
        path={'ARTICLE_COMPOSE'}
      >
        <ArticleComposePage />
      </AppComponentRenderer>
    )
  }
})
