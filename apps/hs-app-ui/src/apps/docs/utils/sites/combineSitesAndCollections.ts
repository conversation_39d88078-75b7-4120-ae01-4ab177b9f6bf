import { Collection, Site } from 'apps/docs/schema'

/**
 * Combines sites and collections by nesting collections within each site
 * based on the matching site id. The current collection is marked as such.
 * @param sites
 * @param collections
 * @param currentCollectionId
 */
export function combineSitesAndCollections(
  sites: Site[],
  collections: Collection[],
  currentCollectionId?: Collection['id']
) {
  return sites.map(site => ({
    collections: collections
      .filter(({ siteId }) => siteId === site.id)
      .map(collection => ({
        id: collection.id,
        isCurrent: currentCollectionId === collection.id,
        name: collection.name,
        siteId: collection.siteId,
      })),
    id: site.id,
    name: site.title,
    hasPublicSite: site.hasPublicSite,
  }))
}
