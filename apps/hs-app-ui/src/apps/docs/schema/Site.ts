/** The site status */
export type SiteStatus = 'active' | 'inactive'

/** A site object */
export type Site = {
  // Unique identifier.
  id: string

  // Indicates if site is active. Possible values: active, inactive.
  status: SiteStatus

  // The subdomain part of the Help Scout URL.
  subDomain: string

  // The optional host used to access the site.
  cname?: string

  // Indicates if the site is available to the public.
  hasPublicSite: boolean

  // The name of the company associated with the site.
  companyName: string

  // The title of the site.
  title: string

  // The optional url that points to the logo to be displayed on the site.
  logoUrl?: string

  // The width (in pixels) of the logo image.
  logoWidth: number

  // The height (in pixels) of the logo image.
  logoHeight: number

  // The optional URL that points to a custom favicon.
  favIconUrl?: string

  // The optional URL that points to a custom touch icon.
  touchIconUrl?: string

  // The URL to the main website for the company.
  homeUrl: string

  // The text to be displayed for the home link.
  homeLinkText: string

  // The optional hex value used to set a custom background color.
  bgColor?: string

  // The optional description that is used in the meta description tag.
  description?: string

  // Indicates if the site shows a contact form.
  hasContactForm: boolean

  // The mailboxId of the Help Scout mailbox that should receive messages sent through the contact form.
  mailboxId?: number

  // The contact form will be sent to this email address if the mailbox id is not specified.
  contactEmail?: string

  // The optional URL used to specify a custom stylesheet.
  styleSheetUrl?: string

  // The optional code that will be inserted into the <head> tag.
  headerCode?: string

  // The id of the Help Scout user who created the site.
  createdBy: number

  // The id of the Help Scout user who last updated the site.
  updatedBy?: number

  // The UTC date and time that the site was created.
  createdAt: string

  // The UTC date and time that the site was last updated.
  updatedAt?: string
}

// Represents a site with collections.
export interface SiteCollection {
  // The site id.
  id: string

  // The site name.
  name: string

  // Whether the site is public.
  hasPublicSite: boolean

  // The site collections.
  collections: Array<{
    // The collection id.
    id: string

    // Whether the collection is the current collection.
    isCurrent: boolean

    // The collection name.
    name: string

    // The site id.
    siteId: string
  }>
}
