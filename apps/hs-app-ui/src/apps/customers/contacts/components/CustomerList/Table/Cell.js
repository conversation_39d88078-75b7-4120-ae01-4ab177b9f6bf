/* eslint-disable react/prop-types */

/* eslint-disable react/display-name */
import cx from 'classnames'

import { formatDate } from '../../../utils/date'

import { SORT_ORDER_DESCENDING } from '../../../constants/params.constants'

import {
  AvatarUI,
  BadgeCountUI,
  ButtonUI,
  CellHeaderTextUI,
  CustomerTitleUI,
  FlexyBlockUI,
  IconHeaderUI,
  OrganizationUI,
  PrimaryTextUI,
  SecondaryTextUI,
  SkeletonAvatarUI,
  SkeletonTextUI,
  SortIconUI,
  TitleOrganizationUI,
  TruncateUI,
} from './Cell.css'

import FilteredList from 'hsds/components/filtered-list'
import Flexy from 'hsds/components/flexy'
import VisuallyHidden from 'hsds/components/visually-hidden'
import CaretDown from 'hsds/icons/caret-down'
import CaretUp from 'hsds/icons/caret-up'

const dedupStrings = strs =>
  strs.filter((value, index, self) => self.indexOf(value) === index)

export const CountCell = ({ ticketCount: count } = {}) => (
  <BadgeCountUI>{count}</BadgeCountUI>
)

export const DateCell = ({ lastSeenAt, modifiedAt } = {}) => (
  <SecondaryTextUI data-timestamp={lastSeenAt || modifiedAt}>
    {formatDate(lastSeenAt || modifiedAt)}
  </SecondaryTextUI>
)

export const EmailCell = ({ customerEmails: emails = [] } = {}) => {
  return emails.length ? (
    <FilteredList
      items={dedupStrings(emails)}
      renderItem={item => (
        <TruncateUI splitter="@" showTooltipOnTruncate={true}>
          {item}
        </TruncateUI>
      )}
      limit={2}
    />
  ) : null
}

export const getHeaderCell = ({
  icon,
  sortCallback = () => {},
  sortOn = false,
} = {}) => {
  return ({ title } = {}, sortedInfo = {}) => {
    const innerContent = icon ? (
      <span>
        <VisuallyHidden>{title}</VisuallyHidden>
        <IconHeaderUI
          data-testid="IconHeaderUI"
          size="24"
          icon={icon}
          data-cy="CustomerList.Header.IconHeader"
        />
      </span>
    ) : (
      <span>{title}</span>
    )
    const isCurrentSortColumn = sortOn === sortedInfo.columnKey

    let direction
    if (!isCurrentSortColumn) {
      direction = 'up'
    } else {
      direction = sortedInfo.order === SORT_ORDER_DESCENDING ? 'down' : 'up'
    }

    const weight = isCurrentSortColumn ? 700 : 500
    const classNames = cx(
      icon && 'is-icon-only',
      !isCurrentSortColumn && 'is-hidden'
    )

    const content = (
      <CellHeaderTextUI weight={weight}>
        {innerContent}
        <SortIconUI
          data-testid="SortIconUI"
          className={classNames}
          icon={direction === 'up' ? CaretUp : CaretDown}
          size="14"
          data-cy="CustomerList.Header.SortIcon"
        />
      </CellHeaderTextUI>
    )

    if (!sortOn) {
      return content
    }

    return (
      <ButtonUI
        color="grey"
        linked
        inlined
        onClick={() => {
          sortCallback(sortOn, sortedInfo)
        }}
      >
        {content}
      </ButtonUI>
    )
  }
}

export const getSkeletonCell =
  ({ withAvatar = false } = {}) =>
  ({ size = 'sm', width = '100%' } = {}) => {
    const text = <SkeletonTextUI data-testid="SkeletonTextUI" width={width} />

    if (!withAvatar) {
      return text
    }

    return (
      <Flexy just="center">
        <Flexy.Item>
          <SkeletonAvatarUI data-testid="SkeletonAvatarUI" size={size} />
        </Flexy.Item>
        <FlexyBlockUI>{text}</FlexyBlockUI>
      </Flexy>
    )
  }

export const NameCell = ({
  customerName: name,
  customerEmails: emails = [],
  jobTitle: title,
  organization,
  thumbnailURL: image,
  defaultAvatarUrl,
} = {}) => {
  const email = getEmail(emails)
  return (
    <Flexy just="center">
      <Flexy.Item>{renderAvatar(image, defaultAvatarUrl)}</Flexy.Item>
      <FlexyBlockUI>
        {renderName(name, email)}
        {renderTitle({ organization, title })}
      </FlexyBlockUI>
    </Flexy>
  )
}

function getEmail(emails = []) {
  return emails.length ? emails[0] : undefined
}

function renderAvatar(image, defaultAvatarUrl) {
  return (
    <AvatarUI
      image={image || defaultAvatarUrl}
      size="sm"
      fallbackImage={defaultAvatarUrl}
    />
  )
}

function renderName(name, email) {
  if (name === email) {
    return null
  }
  return (
    <PrimaryTextUI
      data-testid="PrimaryTextUI"
      data-cy="Name.Name"
      size={14}
      weight={500}
    >
      {name}
    </PrimaryTextUI>
  )
}

function renderTitle({ organization, title }) {
  if (!organization && !title) {
    return null
  }

  return (
    <TitleOrganizationUI data-cy="Name.Title">
      {title && (
        <CustomerTitleUI>
          {title}
          {organization && ', '}
        </CustomerTitleUI>
      )}
      {organization && <OrganizationUI>{organization}</OrganizationUI>}
    </TitleOrganizationUI>
  )
}
