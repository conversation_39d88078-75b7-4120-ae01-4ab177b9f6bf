import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, getHeader<PERSON>ell } from '.'

import { PARAMS } from '../../../constants'

export const getTableConfig = ({ sortCallback = () => {} } = {}) => ({
  columns: [
    {
      title: 'Customers',
      columnKey: [
        'customerName',
        'customerEmails',
        'jobTitle',
        'organization',
        'thumbnailURL',
        'defaultAvatarUrl',
      ],
      renderCell: NameCell,
      renderHeaderCell: getHeaderCell({
        sortCallback,
        sortOn: PARAMS.SORT_FIELD_NAME,
      }),
      width: '53%',
    },
    {
      title: 'Email',
      columnKey: 'customerEmails',
      renderCell: EmailCell,
      renderHeaderCell: getHeaderCell(),
      width: '28%',
    },
    {
      title: 'Conversations',
      columnKey: 'ticketCount',
      renderCell: CountCell,
      renderHeaderCell: getHeaderCell({
        sortCallback,
        sortOn: PARAMS.SORT_FIELD_TICKET_COUNT,
      }),
      width: '120px',
    },
    {
      title: 'Last Seen',
      columnKey: ['lastSeenAt', 'modifiedAt'],
      renderCell: DateCell,
      renderHeaderCell: getHeaderCell({
        sortCallback,
        sortOn: PARAMS.SORT_FIELD_LAST_SEEN_AT,
      }),
      width: '120px',
    },
  ],
})
