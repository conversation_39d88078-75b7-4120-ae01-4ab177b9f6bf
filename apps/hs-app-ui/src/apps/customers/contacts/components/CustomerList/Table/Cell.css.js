import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import Avatar from 'hsds/components/avatar'
import BadgeCount from 'hsds/components/badge-count'
import Button from 'hsds/components/button'
import Flexy from 'hsds/components/flexy'
import Icon from 'hsds/components/icon'
import Skeleton from 'hsds/components/skeleton'
import Text from 'hsds/components/text'
import Truncate from 'hsds/components/truncate'

export const AvatarUI = styled(Avatar)`
  height: 32px !important;
  width: 32px !important;

  .c-Avatar__crop {
    color: ${getColor('charcoal.200')}!important;
  }
`

export const BadgeCountUI = styled(BadgeCount)`
  display: inline-block;
  display: inline-flex;
  margin-left: 4px;
  font-size: 11px;
  font-weight: 400;
  line-height: 16px;
  height: 22px;
  flex-wrap: nowrap;
  align-items: center;
  -webkit-font-smoothing: antialiased;
`

export const IconHeaderUI = styled(Icon)`
  left: 3px;
  position: relative;
`

export const CellHeaderTextUI = styled(Text)`
  color: ${getColor('charcoal.700')};
  display: inline-flex;
  align-items: center;
`

export const ButtonUI = styled(Button)`
  &.is-style-link {
    --hsds-button-color: ${getColor('charcoal.700')};
    --hsds-button-hover-color: ${getColor('charcoal.1100')};
    position: relative;

    &:hover {
      text-decoration: none;
      ${CellHeaderTextUI} {
        color: var(--hsds-button-hover-color);
      }
    }
  }
`

// We need a specific left-margin size that is not supported
// by HSDS-React. Given its 1px smaller than the lg size, it
// does not warrant supporting it.
export const FlexyBlockUI = styled(Flexy.Block)`
  margin-left: 15px !important;
`
export const SortIconUI = styled(Icon)`
  margin-left: 5px;

  &.is-hidden {
    color: transparent;

    button:hover & {
      color: ${getColor('charcoal.500')} !important;
    }
  }

  &.is-icon-only {
    margin-left: 0;
  }
`

export const PrimaryTextUI = styled(Text)`
  color: ${getColor('charcoal.1100')};
`

export const SecondaryTextUI = styled(Text)`
  color: ${getColor('charcoal.700')};
`

export const CustomerTitleUI = styled(Text)`
  display: block;
  color: ${getColor('charcoal.700')};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 3px;
`
export const OrganizationUI = styled(Text)`
  color: ${getColor('charcoal.700')};
  font-weight: bold;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`
export const TitleOrganizationUI = styled('div')`
  display: flex;
  flex-wrap: wrap;
`

export const SkeletonAvatarUI = styled(Skeleton.Avatar)`
  margin-bottom: 0 !important;
`

export const SkeletonTextUI = styled(Skeleton.Text)`
  margin-bottom: 0 !important;
`

// Todo: Remove this style override after this bug
// has been fixed in the Truncate component in
// HSDS-React.
// Jira Issue: https://helpscout.atlassian.net/browse/HSDS-77
export const TruncateUI = styled(Truncate)`
  span:first-child {
    min-width: 0 !important;
  }
`
