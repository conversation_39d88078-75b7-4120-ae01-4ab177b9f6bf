import axios from 'axios'
import PropTypes from 'prop-types'
import { PureComponent } from 'react'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'

import { getCustomersBasePath } from '../../utils/routing'
import { MAILBOX_FILTER_ACTIONS } from '../SearchBar/SearchBar.utils'
import { updateSearchParams, updateSortParams } from './utils'

import { PARAMS } from '../../constants'

import { customerProfileApi, customersApi } from '../../actions'
// REFACTORING: using lodash/debounce instead of debounce from common
import debounce from 'lodash/debounce'

// REFACTORING: try window HS since import doesn't work
const HS = window.HS

const CancelToken = axios.CancelToken

const defaultParams = {
  page: PARAMS.DEFAULT_PAGE,
  query: PARAMS.DEFAULT_QUERY,
  mailboxes: PARAMS.DEFAULT_MAILBOX_IDS,
  sortField: PARAMS.DEFAULT_SORT_FIELD,
  sortOrder: PARAMS.DEFAULT_SORT_ORDER,
}

const VALID_SORT_FIELDS = [
  PARAMS.SORT_FIELD_LAST_SEEN_AT,
  PARAMS.SORT_FIELD_NAME,
  PARAMS.SORT_FIELD_TICKET_COUNT,
]

export const withController = ControlledComponent => {
  class ControllerComponent extends PureComponent {
    static propTypes = {
      debounceTime: PropTypes.number,
      location: PropTypes.shape({
        search: PropTypes.string.isRequired,
        pathname: PropTypes.string.isRequired,
      }).isRequired,
      history: PropTypes.shape({
        push: PropTypes.func.isRequired,
      }).isRequired,
      getCustomers: PropTypes.func.isRequired,
      getCustomer: PropTypes.func.isRequired,
    }

    static defaultProps = {
      debounceTime: 300,
    }

    cancelTokens = {}

    constructor(props) {
      super(props)
      this.updateSearch = debounce(this.updateSearch, props.debounceTime)
    }

    componentDidMount() {
      this._isMounted = true
    }

    UNSAFE_componentWillMount() {
      const params = this.getParams()
      this.fetchAll(params)
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
      this.attemptFetch(nextProps)
    }

    componentWillUnmount() {
      this._isMounted = false
    }

    attemptFetch(nextProps) {
      const { location: previousLocation } = this.props
      const { location: nextLocation } = nextProps

      if (previousLocation.search !== nextLocation.search) {
        const params = this.getParams(nextProps)
        this.fetchAll(params)
      }
    }

    cancelFetch(key) {
      if (this.cancelTokens[key]) {
        this.cancelTokens[key].cancel()
        delete this.cancelTokens[key]
      }
    }

    fetchAll(params) {
      this.cancelFetch('fetchAll')

      return this.props.getCustomers(params, {
        cancelToken: this.getCancelToken('fetchAll'),
      })
    }

    fetchById(id) {
      this.cancelFetch('fetchById')

      return this.props.getCustomer(
        { id },
        { cancelToken: this.getCancelToken('fetchById') }
      )
    }

    getCancelToken(key) {
      this.cancelTokens[key] = CancelToken.source()
      return this.cancelTokens[key].token
    }

    getParams(props = this.props) {
      const {
        location: { search },
      } = props
      const searchParams = new URLSearchParams(search)
      const params = {}

      searchParams.forEach((value, key) => {
        params[key] = decodeURIComponent(value)
      })

      return params
    }

    updateSearch = params => {
      const searchParams = updateSearchParams({
        defaultParams,
        params,
        prevQuery: this.getParams()[PARAMS.QUERY],
      })
      const search = Object.keys(searchParams)
        .map(key => `${key}=${encodeURIComponent(searchParams[key])}`)
        .join('&')
      this.props.history.push({ search })
    }

    changeSort = ({ sortField, sortInfo }) => {
      if (!VALID_SORT_FIELDS.includes(sortField)) {
        return
      }
      const params = updateSortParams({
        sortField,
        sortInfo,
        prevParams: this.getParams(),
      })
      this.updateSearch(params)
    }

    changePage = page => {
      // When the input is focused we should prevent the page from
      // changing. j/k keys are used for pagination, however,
      // during focus, they should be treated as user input.
      if (this.isSearchFocused) {
        return
      }

      const params = this.getParams()
      if (params.page !== page) {
        this.updateSearch({
          ...params,
          page,
        })
      }
    }

    changeQuery = query => {
      const params = this.getParams()
      if (params.query !== query) {
        this.updateSearch({
          ...params,
          query,
          page: 1, // reset page when changing the search term
        })
      }
    }

    changeMailboxIds = ({ mailboxId, action }) => {
      const params = this.getParams()
      let mailboxes

      if (action === MAILBOX_FILTER_ACTIONS.ADD) {
        mailboxes = params.mailboxes
          ? `${params.mailboxes},${mailboxId}`
          : mailboxId
      } else if (action === MAILBOX_FILTER_ACTIONS.REMOVE) {
        mailboxes = params.mailboxes
          .split(',')
          .filter(id => id !== mailboxId)
          .join(',')
      } else if (action === MAILBOX_FILTER_ACTIONS.REMOVE_ALL) {
        mailboxes = ''
      }

      if (params.mailboxes !== mailboxes) {
        this.updateSearch({
          ...params,
          page: 1,
          mailboxes,
        })
      }
    }

    handleSearchBlur = () => {
      this.isSearchFocused = false
    }

    handleSearchFocus = () => {
      this.isSearchFocused = true
    }

    getBasePath = () => {
      const { location } = this.props
      return getCustomersBasePath(location)
    }

    handleRowClick = (e, customer) => {
      e.preventDefault()
      const basePath = this.getBasePath()

      if (e.ctrlKey || e.metaKey) {
        return window.open(`${basePath}/${customer.id}`, '_blank')
      }

      this.fetchById(customer.id)
        .then(data => {
          if (data instanceof Error) {
            throw new Error('Customer not found')
          }
          // Add prefetched flag to the location state
          this.props.history.push({
            pathname: `${basePath}/${customer.id}`,
            state: { prefetched: true },
          })
        })
        .catch(error => {
          HS.Utils.Main.error(error.message, {
            timeout: 3000,
          })
        })
    }

    render() {
      const params = this.getParams()
      return (
        <ControlledComponent
          updateSearch={this.updateSearch}
          changeSort={this.changeSort}
          changeQuery={this.changeQuery}
          changePage={this.changePage}
          changeMailboxIds={this.changeMailboxIds}
          handleSearchBlur={this.handleSearchBlur}
          handleSearchFocus={this.handleSearchFocus}
          handleRowClick={this.handleRowClick}
          selectedMailboxes={params.mailboxes}
          query={params.query}
          sortField={
            params.query
              ? params.sortField
              : params.sortField || defaultParams.sortField
          }
          sortOrder={
            params.query
              ? params.sortOrder
              : params.sortOrder || defaultParams.sortOrder
          }
        />
      )
    }
  }

  const mapDispatchToProps = {
    getCustomer: customerProfileApi.getCustomerProfile,
    getCustomers: customersApi.getCustomers,
  }
  return withRouter(connect(null, mapDispatchToProps)(ControllerComponent))
}
