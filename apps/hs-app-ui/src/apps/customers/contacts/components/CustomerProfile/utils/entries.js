/* eslint-disable unused-imports/no-unused-vars, unused-imports/no-unused-imports */
import { getCustomersBasePath } from '../../../utils/routing'

/* global App */

// REFACTORING: try window HS since import doesn't work
const HS = window.HS

const safeToLowerCase = str =>
  typeof str === 'string' ? str.toLowerCase() : str

export const getProperty = name => {
  const index = String(name).lastIndexOf('_')
  return index > 0 ? String(name).slice(0, index) : name
}

export const getPatchKey = ({ customerId, property }) => {
  return `${customerId}_patch_${property}`
}

export const getPatchEntryKey = ({
  commit: { item = {} } = {},
  customerId,
  property,
}) => {
  return `${customerId}_patch-entry_${property}_${item.id}`
}

export const doesEmailExist = ({
  commit: { item = {} } = {},
  name,
  property,
  values = [],
}) =>
  property === 'emails' &&
  values
    .filter(({ id }) => id !== name)
    .map(({ value }) => safeToLowerCase(value))
    .includes(safeToLowerCase(item.value))

const getAddress = ({
  lines = [''],
  city = '',
  state = '',
  country = '',
  postalCode = '',
}) => {
  const streetAddress = lines.join(' ')

  const nonNullElements = [
    streetAddress,
    city,
    state,
    postalCode,
    country,
  ].filter(item => !!item)
  return nonNullElements.join(', ')
}

const getConflicts = conflictData => {
  const labelMap = {
    address: 'Address',
    background: 'Notes',
    firstName: 'First Name',
    jobTitle: 'Job Title',
    lastName: 'Last Name',
    organization: 'Company',
  }

  return conflictData.map(conflict => {
    const {
      field,
      name,
      propertyType,
      type,
      discard: { value },
    } = conflict

    if (type === 'profileField') {
      if (field === 'address') {
        return [labelMap[field], getAddress(value)]
      }
      return [labelMap[field], value]
    }

    if (propertyType === 'bool') {
      const text = value ? 'Yes' : 'No'

      return [name, text]
    }

    return [name, value]
  })
}
export const handleCreateOrUpdateEntry = (foo = {}, callback = () => {}) => {
  const { id: _id } = foo
  // Rather than just pass the first argument of this function to the callback,
  // we create a new object of allowed properties. The callback is used to handle
  // updating the EditableField and we only ever want to update its _id.
  callback({ updatedProps: _id ? { _id } : {} })
}

export const handleAddEmailWithoutMerging = ({
  api,
  closeMergeModal,
  customerId,
  email,
  errorHandler = () => {},
  previousEmails,
  updateEmails,
}) => {
  const data = { value: email, location: 'work' }
  return api
    .addCustomerEmailWithoutMerging({
      customerId,
      data,
    })
    .then(res => {
      const {
        data: { id },
      } = res
      const emailList = previousEmails.map(email => {
        // Update the temporary email with the ID returned from the api call
        // and the location
        if (!email._id || isNaN(email._id)) {
          return { ...email, _id: id, location: 'work' }
        }

        return email
      })

      // Short-circuit if the merge profile modal is being shown
      // in the convo sidebar so state updates and modal close
      // can be handled in Marionette code
      if (!updateEmails) {
        closeMergeModal()
        return
      }

      updateEmails({ id: customerId, emails: emailList })
      closeMergeModal()
    })
    .catch(() => {
      errorHandler(
        'A technical error prevented this profile from being saved without merging.'
      )
    })
}

export const handleMergeProfiles = ({
  api,
  discardProfile,
  keeperProfile,
  isProfileDialogOpen = false,
  isMailboxView = false,
}) => {
  const { id: discardId, fullName: discardName } = discardProfile
  const { id: keeperId, name: keeperName } = keeperProfile
  let message
  return api
    .mergeCustomers({ discardId, keeperId })
    .then(() => {
      if (discardName && keeperName) {
        message = `Successfully merged ${discardName} into ${keeperName}`
      } else {
        message = 'Successfully merged profiles'
      }
      if (!isMailboxView) {
        if (isProfileDialogOpen) {
          const basePath = getCustomersBasePath()
          window.location = `${basePath}/${keeperId}`
        } else {
          // We need to reload the page instead of using `updateEmails`
          // to update local state and cause a re-render, because the
          // merge endpoint does not return an _id like the endpoint
          // that adds an email without merging. Since there is no
          // _id, we can't accurately update the local copy of state.
          // An email stored in state without an _id will cause bugs.
          window.location.reload()
        }
      }
    })
    .then(() => HS.Utils.Main.success(message))
    .catch(() => HS.Utils.Main.error('Unable to merge profiles'))
}

export const validateEmailInUseOnDifferentProfile = ({
  api,
  customer,
  item,
  openMergeModal,
  resolve = null,
  saveCallback,
  updateEmails,
  values: editedEmailList,
}) => {
  const { id: customerId, customerName, emails: savedEmailList } = customer
  return api
    .checkCustomerEmailInUse({ id: customerId, email: item.value })
    .then(({ data: { email, fullName, id } } = {}) => {
      api.getConflicts({ discardId: id, keeperId: customerId }).then(res => {
        if (updateEmails) {
          // We need to update the internal customer state to contain the new email
          // so that the text doesn't disappear from the EditableField once `resolve()`
          // is called. We are adding the email data to state without an `_id`,
          // property on the object so that we can easily filter it out later.
          updateEmails({
            id: customerId,
            emails: editedEmailList,
          })
        }

        // If the user replaced a saved email with a value that triggered the merge
        // profile modal UX flow, we need to keep track of the removed email so that
        // it can be restored if the modal is cancelled. Delete everything from the
        // original list that does not exist in the edited list to find the removed
        // email.
        const emailListCopy = [...savedEmailList]

        for (var i = 0, len = editedEmailList.length; i < len; i++) {
          for (var j = 0, len2 = emailListCopy.length; j < len2; j++) {
            if (editedEmailList[i].value === emailListCopy[j].value) {
              emailListCopy.splice(j, 1)
              len2 = emailListCopy.length
            }
          }
        }

        // `resolve` is only passed with a value in the flow from Profile Dialog via Convo
        // Sidebar. We handle keeping track of the emails differently and need to
        // explicitly resolve the promise when the modal closes.
        openMergeModal({
          discardProfile: { id, email, fullName },
          discardProperties: getConflicts(res.data),
          emails: editedEmailList,
          keeperProfile: { id: customerId, name: customerName },
          duplicateEmail: item.value,
          removedEmail: emailListCopy.length === 1 ? emailListCopy[0] : null,
          resolve,
        })
      })
    })
    .catch(err => {
      if (err.response && err.response.status == 404) {
        // Responds with a 404 if there is no conflicting customer email
        return saveCallback()
      }

      console.log(err)
    })
}
