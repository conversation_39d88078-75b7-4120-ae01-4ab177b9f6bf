import axios from 'axios'
import cx from 'classnames'
import PropTypes from 'prop-types'
import { Component, useContext } from 'react'
import { connect } from 'react-redux'

import { getCustomerApiClient } from '../../../utils/api'
import { convertPropertyErrorMessage } from '../../../utils/errors'
import { getCustomersBasePath } from '../../../utils/routing'
import {
  doesEmailExist,
  getPatchEntryKey,
  getPatchKey,
  getProperty,
  handleCreateOrUpdateEntry,
  validateEmailInUseOnDifferentProfile,
} from '../utils/entries'
import { Keys } from 'hsds/utils/keyboard'

import {
  LoadingUI,
  ModalBodyUI,
  ModalHeaderUI,
  ModalUI,
  WrapperUI,
} from './ProfileDialog.css'

import { customerProfilePhotoDeleteApi, customers } from '../../../actions'
import {
  customerProfile,
  defaultAvatarUrl,
  features,
  selects,
} from '../../../selectors'
import EditProfileTab from '../EditProfileTab'
// Import the non-connected Header since we may need to
// use this component outside of Redux.
import { Header } from '../Header/Header'
import Loading from '../Loading'
import { GlobalContext, Provider as HSDSProvider } from 'hsds/components/hsds'
import KeypressListener from 'hsds/components/keypress-listener'

// REFACTORING: inlined getCurrentTheme function
export function getCurrentTheme() {
  return 'default'
}

const getComponentClassName = ({ className }) =>
  cx('c-ProfileDialog', className)

const CancelToken = axios.CancelToken

const HSDSProviderWrapper = props => {
  const themeName = getCurrentTheme()

  return (
    <HSDSProvider scope="hsds-react" themeName={themeName}>
      {props.children}
    </HSDSProvider>
  )
}
HSDSProviderWrapper.propTypes = {
  children: PropTypes.any,
}

export class ProfileDialog extends Component {
  static propTypes = {
    autoFocusEmail: PropTypes.bool,
    canUpdate: PropTypes.bool,
    _className: PropTypes.string,
    className: PropTypes.string,
    customer: PropTypes.object,
    defaultAvatarUrl: PropTypes.string,
    deleteCustomerPhoto: PropTypes.func,
    discardProfile: PropTypes.object,
    discardProperties: PropTypes.arrayOf(PropTypes.object),
    isCustomerPropertiesEnabled: PropTypes.bool,
    isOpen: PropTypes.bool,
    isLoading: PropTypes.bool,
    keeperProfile: PropTypes.object,
    hsdsScope: PropTypes.string,
    _mergeCallback: PropTypes.func,
    _onDismiss: PropTypes.func,
    _onEdit: PropTypes.func,
    _onUpdateCustomer: PropTypes.func,
    onDismiss: PropTypes.func,
    onEdit: PropTypes.func,
    onUpdateCustomer: PropTypes.func,
    openMergeModal: PropTypes.func,
    next: PropTypes.func,
    phones: PropTypes.array,
    prev: PropTypes.func,
    updateEmails: PropTypes.func,
    _patchCustomerProfile: PropTypes.func,
    patchCustomerProfile: PropTypes.func,
    highlightedProp: PropTypes.string,
  }

  static defaultProps = {
    autoFocusEmail: false,
    canUpdate: true,
    customer: {},
    isOpen: false,
    isLoading: false,
    hsdsScope: '',
    onDismiss: () => {},
    onEdit: () => {},
    onUpdateCustomer: () => {},
    openMergeModal: () => {},
    phones: [],
  }

  cancelTokens = {}

  constructor(props) {
    super(props)
    this.customerApi = getCustomerApiClient()
    this.state = {
      isOpen: this.props.isOpen || false,
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.customer.id !== this.props.customer.id) {
      this.customerApi = getCustomerApiClient()
    } else if (nextProps.isOpen !== this.props.isOpen) {
      this.setState({
        isOpen: nextProps.isOpen,
      })
    }
  }

  componentWillUnmount() {
    Object.keys(this.cancelTokens).forEach(key =>
      this.cancelTokens[key].cancel()
    )
  }

  cancel(key) {
    if (this.cancelTokens[key]) {
      this.cancelTokens[key].cancel()
    }
  }

  getCancelToken(key) {
    this.cancelTokens[key] = CancelToken.source()
    return this.cancelTokens[key].token
  }

  handleOnClose = () => {
    const { onDismiss } = this.props

    if (onDismiss) {
      onDismiss()
    }
  }

  handleEscape = () => {
    if (!this.isEditing()) {
      this.setState({
        isOpen: false,
      })
    }
  }

  isEditing = () => {
    if (!this.formNode || !this.headerNode) {
      return false
    }

    const focusedFormNodes = this.formNode.querySelectorAll(':focus') || []
    const focusedHeaderNodes = this.headerNode.querySelectorAll(':focus') || []
    const unfocusedFormTextarea =
      this.formNode.querySelectorAll(
        '.EditableTextarea__ResizableTextarea.is-readonly'
      ) || []

    return (
      focusedFormNodes.length > 0 ||
      focusedHeaderNodes.length > 0 ||
      unfocusedFormTextarea.length === 0
    )
  }

  patch = ({ name = '', value = '' } = {}) =>
    new Promise(resolve => {
      const {
        customer: { id: customerId },
        onUpdateCustomer,
      } = this.props
      const property = getProperty(name)
      const key = getPatchKey({ customerId, property })
      this.cancel(key)
      return this.customerApi
        .patch(
          {
            customerId,
            property,
            value,
          },
          {
            cancelToken: this.getCancelToken(key),
          }
        )
        .then(() => {
          onUpdateCustomer({ customerId, property, value })
          return resolve({
            isValid: true,
            name,
          })
        })
        .catch(err =>
          resolve({
            isValid: false,
            name,
            message: this.customerApi.getErrorMessage(err),
            type: 'error',
          })
        )
    })

  patchEntry = ({ data: commit = {}, name = '', values = [] } = {}) =>
    new Promise(resolve => {
      const {
        customer: { id: customerId },
        _mergeCallback,
        onUpdateCustomer,
        openMergeModal,
        updateEmails,
      } = this.props
      const property = getProperty(name)
      const key = getPatchEntryKey({ commit, customerId, property })
      this.cancel(key)

      if (doesEmailExist({ commit, name, property, values })) {
        return resolve({
          isValid: false,
          name,
          message: 'Email already exists',
          type: 'error',
        })
      }

      const saveCallback = (query = {}) => {
        this.customerApi
          .patchEntry(
            {
              commit,
              customerId,
              name,
              property,
              values,
              query,
            },
            {
              cancelToken: this.getCancelToken(key),
            }
          )
          .then(({ data }) => {
            if (property === 'emails') {
              // We need to update internal state when emails are added because we rely
              // on that state in the merge profile process. If we don't update the list
              // on creation, when a user first adds a unique email and THEN adds an email
              // that is in use by another profile, we will have an outdated internal
              // email list and the unique first email will disappear from the UX when
              // the merge profile modal closes and only be displayed on refresh.
              if (updateEmails) {
                const emailList = values.filter(email => email.location)
                const updatedEmails = data
                  ? [
                      ...emailList,
                      {
                        _id: data.id,
                        location: 'work',
                        value: commit.item.value,
                      },
                    ]
                  : values
                updateEmails({
                  id: customerId,
                  emails: updatedEmails,
                })
              }
            }
            return handleCreateOrUpdateEntry(data, ({ updatedProps }) => {
              onUpdateCustomer({ customerId, property, values })
              return resolve({
                isValid: true,
                name,
                updatedProps,
              })
            })
          })
          .catch(err =>
            resolve({
              isValid: false,
              name,
              message: this.customerApi.getErrorMessage(err),
              type: 'error',
            })
          )
      }

      if (property !== 'emails') {
        return saveCallback({})
      }

      function mergeModalResolve({
        isProfileDialog = false,
        isCancel = false,
      } = {}) {
        // We need to call `resolve` so that the EditableField is not in a disabled
        // state when the modal closes. We also need to keep track of the value of
        // the field via internal state using the `updateEmails` function passed to
        // `validateEmailInUseOnDifferentProfile`.
        // Note: the `updateEmails` function is different for each UX flow - Full Profile,
        // ProfileDialog via Customer List, and ProfileDialog via Convo Sidebar

        // If we are in the ProfileDialog (via Convo Sidebar or Customer List)
        // instead of in the Full Customer Profile:
        if (isProfileDialog) {
          if (!isCancel) {
            // This maintains the email value in the EditableField after clicking
            // on 'Add Without Merging' or 'Merge' in the MergeProfileModal
            return resolve({ isValid: true, name })
          }
          // This removes the email value from the EditableFeld after clicking
          // 'Cancel' in the MergeProfileModal
          return resolve()
        }

        if (isCancel) {
          return resolve()
        }
        return resolve({ isValid: true, name })
      }

      return validateEmailInUseOnDifferentProfile({
        api: this.customerApi,
        customer: this.props.customer,
        item: commit.item,
        openMergeModal,
        resolve: mergeModalResolve,
        saveCallback,
        updateEmails,
        values,
      })
    })

  patchByDelete = ({ data: commit = {}, name = '' }) => {
    if (commit.operation !== 'DELETE' || !commit.item || !commit.item.id) {
      return
    }
    const {
      customer: { id: customerId },
      onUpdateCustomer,
    } = this.props
    const property = getProperty(name)
    const key = getPatchKey({ customerId, property })

    // Deleting on single-entry fields means to discard the value.
    const value = ''

    this.cancel(key)
    // Delete operations are optimistic. We do not
    // validate them. Fire and forget. If the user edits
    // this field after deleting and this request is still
    // in flight it will be cancelled so the edit made after
    // delete will come in last as expected.
    this.customerApi.patch(
      {
        customerId,
        property,
        value,
      },
      {
        cancelToken: this.getCancelToken(key),
      }
    )
    onUpdateCustomer({ customerId, property, value })
  }

  deleteEntry = ({ data: commit = {}, name = '', value }) => {
    if (commit.operation !== 'DELETE' || !commit.item || !commit.item.id) {
      return
    }

    const {
      customer: { id: customerId, emails },
      onUpdateCustomer,
      updateEmails,
    } = this.props
    const property = getProperty(name)
    const key = getPatchEntryKey({ commit, customerId, property })
    this.cancel(key)

    if (property === 'emails' && updateEmails) {
      // TODO - Fix bug where you can't delete an email immediately after
      // adding it to a profile via "Add Without Merge"
      commit.item._id = customerId
      const updatedEmails = emails.filter(
        email => email._id !== commit.item._id
      )
      updateEmails({ id: customerId, emails: updatedEmails })
    }

    this.customerApi
      .deleteEntry(
        {
          commit,
          customerId,
          name,
          property,
        },
        {
          cancelToken: this.getCancelToken(key),
        }
      )
      .then(() => {
        onUpdateCustomer({ customerId, property, values: value })
      })
      .catch(err => console.error(err))
  }

  getActionUrl = ({ customerId = '' } = {}) => {
    const basePath = getCustomersBasePath()
    return `${basePath}/${customerId}`
  }

  patchProperty = ({ name, slug, type, value }) => {
    return new Promise(resolve => {
      const {
        customer: { id: customerId, properties },
        onUpdateCustomer,
      } = this.props

      const property = getProperty(name)
      const key = getPatchKey({ customerId, property })
      this.cancel(key)
      return this.customerApi
        .patchProperty(
          {
            customerId,
            slug,
            value,
          },
          {
            cancelToken: this.getCancelToken(key),
          }
        )
        .then(() => {
          const data = {
            customerId,
            property: 'properties',
            slug,
            value,
          }

          if (type === 'dropdown' && value) {
            const { label } = properties
              .find(property => property.slug === slug)
              .options.find(({ id }) => id === value)
            data.label = label
          }

          onUpdateCustomer(data)
          resolve({
            isValid: true,
            name,
          })
        })
        .catch(err => {
          let message = this.customerApi.getErrorMessage(err)

          message = convertPropertyErrorMessage(message, slug, type, value)

          resolve({
            isValid: false,
            name,
            message,
            type: 'error',
          })
        })
    })
  }

  render() {
    const {
      autoFocusEmail,
      canUpdate,
      _className,
      customer,
      defaultAvatarUrl,
      deleteCustomerPhoto,
      hsdsScope,
      isCustomerPropertiesEnabled,
      isLoading,
      isOpen: _isOpen,
      _onDismiss,
      _onUpdateCustomer,
      _onEdit,
      _patchCustomerProfile,
      phones,
      highlightedProp,
      ...rest
    } = this.props
    const { isOpen } = this.state

    if (!customer) {
      return null
    }
    const componentClassName = getComponentClassName(this.props)

    const actionLabel = `${canUpdate ? 'Edit' : 'View'} Full Profile`

    return (
      <HSDSProviderWrapper>
        <WrapperUI
          className={componentClassName}
          {...rest}
          data-testid="CustomersProfileDialog"
          data-cy="Customers.ProfileDialog"
        >
          <ModalUI
            hsApp={true}
            closeOnEscape={false}
            closeIcon={false}
            isOpen={isOpen}
            onClose={this.handleOnClose}
            wrapperClassName={hsdsScope}
          >
            {isLoading && <LoadingUI />}
            <ModalHeaderUI>
              <div ref={node => (this.headerNode = node)}>
                {!isLoading && (
                  <Header
                    canUpdate={canUpdate}
                    customer={customer}
                    defaultAvatarUrl={defaultAvatarUrl}
                    deleteCustomerPhoto={deleteCustomerPhoto}
                    actionLabel={actionLabel}
                    getActionUrl={this.getActionUrl}
                    patchCustomerProfile={this.patch}
                    showWebsiteList
                  />
                )}
                {isLoading && (
                  <Loading.Header
                    data-cy="Customers.ProfileDialog.LoadingHeader"
                    showWebsiteList
                  />
                )}
              </div>
            </ModalHeaderUI>
            <ModalBodyUI isScrollLocked={false}>
              {!isLoading && (
                <div ref={node => (this.formNode = node)}>
                  <EditProfileTab
                    autoFocusEmail={autoFocusEmail}
                    canUpdate={canUpdate}
                    data-testid="EditProfileTab"
                    data-cy="Customers.ProfileDialog.EditProfile"
                    customer={customer}
                    deleteCustomerProfileEntry={this.deleteEntry}
                    isCustomerPropertiesEnabled={isCustomerPropertiesEnabled}
                    patchCustomerProfile={this.patch}
                    patchCustomerProfileByDelete={this.patchByDelete}
                    patchCustomerProfileEntry={this.patchEntry}
                    patchCustomerProperty={this.patchProperty}
                    phones={phones}
                    highlightedProp={highlightedProp}
                  />
                  <KeypressListener
                    handler={this.handleEscape}
                    keyCode={Keys.ESCAPE}
                    type="keydown"
                  />
                </div>
              )}
              {isLoading && (
                <Loading.Body data-cy="Customers.ProfileDialog.LoadingBody" />
              )}
            </ModalBodyUI>
          </ModalUI>
        </WrapperUI>
      </HSDSProviderWrapper>
    )
  }
}

const mapDispatchToProps = {
  deleteCustomerPhoto: customerProfilePhotoDeleteApi.deleteCustomerProfilePhoto,
  onUpdateCustomer: customers.updateProperty,
}

const mapStateToProps = state => ({
  // TODO: The customer prop that we pass needs to have the same shape as whatever this selector returns - this
  // customer isn't nested and may have different attributes
  customer: customerProfile.customerSelector(state),
  defaultAvatarUrl: defaultAvatarUrl.defaultAvatarUrlSelector(state),
  isCustomerPropertiesEnabled:
    features.isCustomerPropertiesEnabledSelector(state),
  phones: selects.phonesSelector(state),
})

export const ProfileDialogWithContext = props => {
  const contextValue = useContext(GlobalContext)
  const hsdsScope = contextValue ? contextValue.getCurrentScope() : null

  return <ProfileDialog {...props} hsdsScope={hsdsScope} />
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ProfileDialogWithContext)
