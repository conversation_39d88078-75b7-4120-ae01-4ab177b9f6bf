import { MemoryRouter } from 'react-router-dom'

import { render } from '@testing-library/react'

import { Navigation } from '../Navigation'

describe('Navigation', () => {
  test('Renders a Navigation', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <Navigation />
      </MemoryRouter>
    )
    expect(getByTestId('CustomersNavigation')).toBeInTheDocument()
  })
})

describe('Conversations tab', () => {
  test('Renders a Conversations tab', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <Navigation />
      </MemoryRouter>
    )

    expect(getByTestId('CustomersNavigationConversations')).toBeInTheDocument()
  })

  test('Renders a Conversations tab with total', () => {
    const count = 10
    const { getAllByText } = render(
      <MemoryRouter>
        <Navigation totalConversations={count} />
      </MemoryRouter>
    )

    expect(getAllByText(`Conversations (${count})`)[0]).toBeInTheDocument()
  })
})

describe('Profile tab', () => {
  test('Renders a Profile tab', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <Navigation />
      </MemoryRouter>
    )
    expect(getByTestId('CustomersNavigationProfile')).toBeInTheDocument()
  })
})

describe('Active tab behavior', () => {
  test('Activates Profile tab based on location', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/customers/123']}>
        <Navigation profileId="123" location={{ pathname: '/customers/123' }} />
      </MemoryRouter>
    )

    const tab = getByTestId('CustomersNavigationProfile')
    expect(tab.getAttribute('aria-current')).toBe('page')
  })

  test('Activates Conversations tab based on location', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/customers/123/conversations']}>
        <Navigation
          profileId="123"
          location={{ pathname: '/customers/123/conversations' }}
        />
      </MemoryRouter>
    )

    const tab = getByTestId('CustomersNavigationConversations')
    expect(tab.getAttribute('aria-current')).toBe('page')
  })
})
