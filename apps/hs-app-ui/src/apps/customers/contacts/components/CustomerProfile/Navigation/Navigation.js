import PropTypes from 'prop-types'
import { Component } from 'react'

import { getCustomersBasePath } from '../../../utils/routing'

import { NavigationUI } from './Navigation.css'

import TabBar from 'hsds/components/tab-bar'

export class Navigation extends Component {
  static propTypes = {
    tab: PropTypes.string,
    profileId: PropTypes.string,
    totalConversations: PropTypes.number,
    location: PropTypes.object,
  }

  static defaultProps = {
    totalConversations: null,
  }

  renderConvosTabContent() {
    const { totalConversations } = this.props
    const label = 'Conversations'
    if (totalConversations === null) {
      return label
    }
    // REFACTORING: replaced formatNumber with parseInt
    return `${label} (${parseInt(totalConversations)})`
  }

  isProfileActive = () => {
    const { profileId, location } = this.props
    const basePath = getCustomersBasePath(location)
    return location?.pathname === `${basePath}/${profileId}`
  }

  isConversationActive = () => {
    const { profileId, location } = this.props
    const basePath = getCustomersBasePath(location)
    return location?.pathname === `${basePath}/${profileId}/conversations`
  }

  render() {
    const basePath = getCustomersBasePath(this.props.location)

    return (
      <NavigationUI
        data-testid="CustomersNavigation"
        data-cy="Customers.Navigation"
      >
        <TabBar align="left" variant="border-bottom" withReactRouter>
          <TabBar.Item
            key="profile"
            data-testid="CustomersNavigationProfile"
            data-cy="Customers.Navigation.Profile"
            to={`${basePath}/${this.props.profileId}`}
            isActive={this.isProfileActive}
            aria-current={this.isProfileActive() ? 'page' : undefined}
          >
            Profile
          </TabBar.Item>
          <TabBar.Item
            key="conversation"
            data-testid="CustomersNavigationConversations"
            data-cy="Customers.Navigation.Conversations"
            to={`${basePath}/${this.props.profileId}/conversations`}
            isActive={this.isConversationActive}
            aria-current={this.isConversationActive() ? 'page' : undefined}
          >
            {this.renderConvosTabContent()}
          </TabBar.Item>
        </TabBar>
      </NavigationUI>
    )
  }
}

export default Navigation
