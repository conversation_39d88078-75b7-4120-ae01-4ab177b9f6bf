/* eslint-disable unused-imports/no-unused-vars */
import axios from 'axios'
import PropTypes from 'prop-types'
import { PureComponent } from 'react'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'

import { getCustomerApiClient } from '../../utils/api'
import { convertPropertyErrorMessage } from '../../utils/errors'
import {
  doesEmailExist,
  getPatchEntryKey,
  getPatchKey,
  getProperty,
  handleAddEmailWithoutMerging,
  handleCreateOrUpdateEntry,
  handleMergeProfiles,
  validateEmailInUseOnDifferentProfile,
} from './utils/entries'

import {
  customerProfile as customerProfileActions,
  customerProfileApi,
} from '../../actions'
import { customerProfile, features } from '../../selectors'

const CancelToken = axios.CancelToken

export const withController = ControlledComponent => {
  class ControllerComponent extends PureComponent {
    static propTypes = {
      // Router props
      match: PropTypes.shape({
        params: PropTypes.shape({
          id: PropTypes.string,
        }),
      }).isRequired,
      location: PropTypes.shape({
        state: PropTypes.object,
      }).isRequired,

      // Data props
      data: PropTypes.shape({
        customer: PropTypes.shape({
          id: PropTypes.string,
        }),
      }),

      // State props
      customer: PropTypes.shape({
        id: PropTypes.string.isRequired,
        emails: PropTypes.arrayOf(
          PropTypes.shape({
            _id: PropTypes.string,
            value: PropTypes.string,
            default: PropTypes.bool,
            location: PropTypes.string,
          })
        ),
        conflicts: PropTypes.shape({
          removedEmail: PropTypes.object,
          duplicateEmail: PropTypes.string,
          discardProfile: PropTypes.shape({
            email: PropTypes.string,
          }),
          keeperProfile: PropTypes.object,
          discardProperties: PropTypes.array,
        }),
      }),
      hasCustomerProfile: PropTypes.bool,
      isCustomerPropertiesEnabled: PropTypes.bool,
      loading: PropTypes.bool,
      mailboxes: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string,
        })
      ),

      // Action props
      closeMergeModal: PropTypes.func.isRequired,
      getCustomerProfile: PropTypes.func.isRequired,
      openMergeModal: PropTypes.func.isRequired,
      updateEmails: PropTypes.func.isRequired,
    }

    cancelTokens = {}

    constructor(props) {
      super(props)
      this.customerApi = getCustomerApiClient()
      this.state = {
        mergeModalErrorMsg: '',
      }
    }

    UNSAFE_componentWillMount() {
      const params = this.getParams()
      const { data, location } = this.props

      // Check if we have a prefetched flag in location state (set by CustomerList navigation)
      const isPrefetched = location.state && location.state.prefetched

      // Set the prefetched state for the component
      this.setState({ prefetched: isPrefetched })

      // Always fetch when mounting, but we'll control UI in the render method
      this.fetch(params)
    }

    componentDidUpdate(prevProps) {
      // Compare previous and current profile ID (match.params.id)
      const prevId = prevProps.match.params.id
      const currentId = this.props.match.params.id

      if (prevId !== currentId) {
        const params = this.getParams()
        // Always refetch when ID changes
        this.customerApi = getCustomerApiClient()
        this.fetch(params)

        // Reset prefetched flag when navigating to a different profile
        this.setState({ prefetched: false })
      }
    }

    componentWillUnmount() {
      Object.keys(this.cancelTokens).forEach(key =>
        this.cancelTokens[key].cancel()
      )
    }

    addEmailWithoutMerge = () => {
      const {
        closeMergeModal,
        customer: { id, conflicts = {}, emails },
        updateEmails,
      } = this.props
      const { discardProfile = {} } = conflicts
      return handleAddEmailWithoutMerging({
        api: this.customerApi,
        closeMergeModal,
        customerId: id,
        email: discardProfile.email,
        errorHandler: msg => this.setState({ mergeModalErrorMsg: msg }),
        previousEmails: emails,
        updateEmails,
      })
    }

    cancel(key) {
      if (this.cancelTokens[key]) {
        this.cancelTokens[key].cancel()
      }
    }

    cancelMergeModal = () => {
      const { closeMergeModal, customer, updateEmails } = this.props
      const { mergeModalErrorMsg } = this.state
      const { removedEmail } = customer.conflicts
      let emailList = [...customer.emails]
      closeMergeModal()

      const duplicateEmailIndex = customer.emails.findIndex(
        email => email.value() === customer.conflicts.duplicateEmail
      )

      if (removedEmail) {
        // If an existing email was replaced with an email that triggered the
        // merge conflict modal UX, and the modal was cancelled, the original
        // email that was removed should be replaced
        emailList.splice(duplicateEmailIndex, 1, removedEmail)
      } else {
        // remove the duplicate email from the list on cancel
        emailList = emailList.filter(
          email => email.value !== customer.conflicts.duplicateEmail
        )
      }

      updateEmails({ id: customer.id, emails: emailList })
      if (mergeModalErrorMsg) {
        this.setState({ mergeModalErrorMsg: '' })
      }
    }

    fetch(params) {
      this.cancel('fetch')

      this.props.getCustomerProfile(params, {
        cancelToken: this.getCancelToken('fetch'),
      })
    }

    getCancelToken(key) {
      this.cancelTokens[key] = CancelToken.source()
      return this.cancelTokens[key].token
    }

    getEmailCustomerUrl = ({ mailboxId = '' } = {}) => {
      const { customer: { id: customerId, emails = [] } = {}, mailboxes = [] } =
        this.props
      if (!customerId || (!mailboxId && !mailboxes.length)) {
        return ''
      }
      const mbxId = mailboxId || mailboxes[0].id
      if (!emails.length) {
        return `/mailbox/${mbxId}/customer/${customerId}`
      }
      const defaultEmails = emails.filter(email => email.default === true)
      const emailId = defaultEmails.length
        ? defaultEmails[0]._id
        : emails[0]._id
      return `/mailbox/${mbxId}/customer/${customerId}/${emailId}`
    }

    getParams(props = this.props) {
      const {
        match: { params },
        location: { search },
      } = props

      const searchParams = new URLSearchParams(search)
      const queryParams = {}
      for (const [key, value] of searchParams.entries()) {
        queryParams[key] = value
      }

      return { ...params, ...queryParams }
    }

    mergeProfiles = () => {
      const {
        customer: { conflicts = {} },
      } = this.props
      const { discardProfile = {}, keeperProfile = {} } = conflicts

      return handleMergeProfiles({
        api: this.customerApi,
        discardProfile,
        keeperProfile,
      })
    }

    openUrl = (e, url) => {
      e && e.preventDefault()

      if (!url) {
        return
      } else if (e && e.metaKey) {
        window.open(url)
      } else {
        window.location = url
      }
    }

    patch = ({ name = '', value = '' } = {}) =>
      new Promise(resolve => {
        const {
          customer: { id: customerId },
        } = this.props
        const property = getProperty(name)
        const key = getPatchKey({ customerId, property })
        this.cancel(key)
        return this.customerApi
          .patch(
            {
              customerId,
              property,
              value,
            },
            {
              cancelToken: this.getCancelToken(key),
            }
          )
          .then(() =>
            resolve({
              isValid: true,
              name,
            })
          )
          .catch(err =>
            resolve({
              isValid: false,
              name,
              message: this.customerApi.getErrorMessage(err),
              type: 'error',
            })
          )
      })

    patchByDelete = ({ data: commit = {}, name = '' }) => {
      if (commit.operation !== 'DELETE' || !commit.item || !commit.item.id) {
        return
      }
      const {
        customer: { id: customerId },
      } = this.props
      const property = getProperty(name)
      const key = getPatchKey({ customerId, property })
      this.cancel(key)
      // Delete operations are optimistic. We do not
      // validate them. Fire and forget. If the user edits
      // this field after deleting and this request is still
      // in flight it will be cancelled so the edit made after
      // delete will come in last as expected.
      this.customerApi.patch(
        {
          customerId,
          property,
          value: '',
        },
        {
          cancelToken: this.getCancelToken(key),
        }
      )
    }

    patchEntry = ({ data: commit = {}, name = '', values = [] } = {}) =>
      new Promise((resolve, reject) => {
        const {
          customer: { id: customerId },
          openMergeModal,
          updateEmails,
        } = this.props
        const property = getProperty(name)
        const key = getPatchEntryKey({ commit, customerId, property })
        this.cancel(key)

        if (doesEmailExist({ commit, name, property, values })) {
          return resolve({
            isValid: false,
            name,
            message: 'Email already exists',
            type: 'error',
          })
        }

        const saveCallback = (query = {}) => {
          this.customerApi
            .patchEntry(
              {
                commit,
                customerId,
                name,
                property,
                values,
                query,
              },
              {
                cancelToken: this.getCancelToken(key),
              }
            )
            .then(({ data }) => {
              if (property === 'emails') {
                // We need to update internal state when emails are added because we rely
                // on that state in the merge profile process. If we don't update the list
                // on creation, when a user first adds a unique email and THEN adds an email
                // that is in use by another profile, we will have an outdated internal
                // email list and the unique first email will disappear from the UX when
                // the merge profile modal closes and only be displayed on refresh.
                const emailList = values.filter(email => email.location)
                const updatedEmails = data
                  ? [
                      ...emailList,
                      {
                        _id: data.id,
                        location: 'work',
                        value: commit.item.value,
                      },
                    ]
                  : values

                updateEmails({
                  id: customerId,
                  emails: updatedEmails,
                })
              }

              handleCreateOrUpdateEntry(data, ({ updatedProps }) => {
                return resolve({
                  isValid: true,
                  name,
                  updatedProps,
                })
              })
            })
            .catch(err =>
              resolve({
                isValid: false,
                name,
                message: this.customerApi.getErrorMessage(err),
                type: 'error',
              })
            )
        }

        if (property !== 'emails') {
          return saveCallback({})
        }

        return validateEmailInUseOnDifferentProfile({
          api: this.customerApi,
          customer: this.props.customer,
          item: commit.item,
          openMergeModal,
          reject,
          resolve,
          saveCallback,
          updateEmails,
          values,
        })
      })

    deleteEntry = ({ data: commit = {}, name = '' }) => {
      if (commit.operation !== 'DELETE' || !commit.item || !commit.item.id) {
        return
      }

      const {
        customer: { id: customerId, emails },
        updateEmails,
      } = this.props
      const property = getProperty(name)
      const key = getPatchEntryKey({ commit, customerId, property })
      this.cancel(key)

      // We need to update internal state when emails are deleted
      // because we rely on that state in the merge profile process. If we don't
      // update the list on delete, when a user first deletes an email and THEN
      // adds an email that is in use by another profile, we will have an
      // outdated internal email list and the deleted email will return to the
      // UX when the merge profile modal opens, until the page is refreshed.
      if (property === 'emails') {
        const updatedEmails = emails.filter(
          email => email._id !== commit.item._id
        )
        updateEmails({ id: customerId, emails: updatedEmails })
      }
      this.customerApi
        .deleteEntry(
          {
            commit,
            customerId,
            name,
            property,
          },
          {
            cancelToken: this.getCancelToken(key),
          }
        )
        .catch(err => console.error(err))
    }

    patchProperty = ({ name, slug, type, value }) => {
      return new Promise(resolve => {
        const {
          customer: { id: customerId },
        } = this.props

        const property = getProperty(name)
        const key = getPatchKey({ customerId, property })
        this.cancel(key)
        return this.customerApi
          .patchProperty(
            {
              customerId,
              slug,
              value,
            },
            {
              cancelToken: this.getCancelToken(key),
            }
          )
          .then(() =>
            resolve({
              isValid: true,
              name,
            })
          )
          .catch(err => {
            let message = this.customerApi.getErrorMessage(err)

            message = convertPropertyErrorMessage(message, slug, type, value)

            resolve({
              isValid: false,
              name,
              message,
              type: 'error',
            })
          })
      })
    }

    render() {
      const params = this.getParams()
      const {
        customer: { conflicts = {} },
        isCustomerPropertiesEnabled,
        mailboxes,
        openMergeModal,
        updateEmails,
      } = this.props

      const { mergeModalErrorMsg } = this.state

      const {
        discardProperties = [],
        discardProfile = {},
        keeperProfile = {},
      } = conflicts

      return (
        <ControlledComponent
          addEmailWithoutMerge={this.addEmailWithoutMerge}
          mergeModalErrorMsg={mergeModalErrorMsg}
          discardProfile={discardProfile}
          cancelMergeModal={this.cancelMergeModal}
          discardProperties={discardProperties}
          getEmailCustomerUrl={this.getEmailCustomerUrl}
          hasMailboxes={mailboxes && !!mailboxes.length}
          isCustomerPropertiesEnabled={isCustomerPropertiesEnabled}
          isMergeModalOpen={!!discardProfile.email}
          keeperProfile={keeperProfile}
          mergeProfiles={this.mergeProfiles}
          openMergeModal={openMergeModal}
          openUrl={this.openUrl}
          deleteCustomerProfileEntry={this.deleteEntry}
          patchCustomerProfile={this.patch}
          patchCustomerProfileByDelete={this.patchByDelete}
          patchCustomerProfileEntry={this.patchEntry}
          patchCustomerProperty={this.patchProperty}
          profileId={params.id}
          tab={params.tab}
          updateEmails={updateEmails}
          location={this.props.location}
          prefetched={this.state.prefetched}
          data={this.state.data}
          error={this.state.error}
          loaded={this.state.loaded}
          loading={this.state.loading}
        />
      )
    }
  }

  const mapDispatchToProps = {
    closeMergeModal: customerProfileActions.closeMergeModal,
    getCustomerProfile: customerProfileApi.getCustomerProfile,
    openMergeModal: customerProfileActions.openMergeModal,
    updateEmails: customerProfileActions.updateEmails,
  }

  const mapStateToProps = state => {
    return {
      customer: customerProfile.customerSelector(state),
      hasCustomerProfile: customerProfile.hasCustomerProfileSelector(state),
      isCustomerPropertiesEnabled:
        features.isCustomerPropertiesEnabledSelector(state),
      loading: customerProfile.loadingSelector(state),
      mailboxes: customerProfile.mailboxesSelector(state),
    }
  }

  return withRouter(
    connect(mapStateToProps, mapDispatchToProps)(ControllerComponent)
  )
}
