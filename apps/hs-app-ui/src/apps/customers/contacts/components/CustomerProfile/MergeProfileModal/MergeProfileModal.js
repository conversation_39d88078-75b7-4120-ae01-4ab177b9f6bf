import PropTypes from 'prop-types'

import { getCustomersBasePath } from '../../../utils/routing'

import { ConflictModalUI, NoConflictModalUI } from './MergeProfileModal.css'

import Alert from 'hsds/components/alert'
import Modal from 'hsds/components/modal'
import Text from 'hsds/components/text'

function getName(name) {
  return name ? name : 'another customer profile'
}

function MergeProfileModal({
  addEmailWithoutMerge,
  mergeModalErrorMsg,
  cancelMergeModal,
  discardProfile,
  discardProperties,
  keeperProfile,
  mergeProfiles,
}) {
  const { isHsAppUiMailboxRewriteEnabled } = true
  const secondaryButtonText = !isHsAppUiMailboxRewriteEnabled
    ? 'Continue Without Merging'
    : null
  const basePath = getCustomersBasePath()

  if (discardProperties.length) {
    return (
      <ConflictModalUI version={2} title="Merge Profiles" isOpen>
        <Modal.Body version={2}>
          {mergeModalErrorMsg && (
            <Alert status="error">{mergeModalErrorMsg}</Alert>
          )}
          <Text className="merge-text" size="14" block>
            You are about to merge{' '}
            <Text weight="700">{discardProfile.email}</Text> with{' '}
            <a href={`${basePath}/${keeperProfile.id}`}>
              {getName(keeperProfile.name)}
            </a>
            . The following values already exist in the new profile and
            won&apos;t be transferred:
            <table>
              <tbody>
                {discardProperties.map(([key, value], index) => (
                  <tr key={index}>
                    <th>
                      <span>{key}</span>
                    </th>
                    <td>
                      <span>{value}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Text>
        </Modal.Body>
        <Modal.ActionFooter
          onCancel={cancelMergeModal}
          onPrimaryClick={mergeProfiles}
          onSecondaryClick={addEmailWithoutMerge}
          primaryButtonText="Merge Profiles"
          secondaryButtonText={secondaryButtonText}
        />
      </ConflictModalUI>
    )
  }
  return (
    <NoConflictModalUI version={2} title="Merge Profiles" isOpen>
      <Modal.Body version={2}>
        {mergeModalErrorMsg && (
          <Alert status="error">{mergeModalErrorMsg}</Alert>
        )}
        <Text size={14} weight="700">
          {discardProfile.email}
        </Text>{' '}
        belongs to{' '}
        <a href={`${basePath}/${discardProfile.id}`}>
          {getName(discardProfile.name)}
        </a>
        . Would you like to merge these two profiles?
      </Modal.Body>
      <Modal.ActionFooter
        onCancel={cancelMergeModal}
        onPrimaryClick={mergeProfiles}
        onSecondaryClick={addEmailWithoutMerge}
        primaryButtonText="Merge Profiles"
        secondaryButtonText={secondaryButtonText}
      />
    </NoConflictModalUI>
  )
}

MergeProfileModal.propTypes = {
  addEmailWithoutMerge: PropTypes.func,
  cancelMergeModal: PropTypes.func,
  discardProfile: PropTypes.object,
  discardProperties: PropTypes.array,
  keeperProfile: PropTypes.object,
  mergeProfiles: PropTypes.func,
  mergeModalErrorMsg: PropTypes.string,
}

export default MergeProfileModal
