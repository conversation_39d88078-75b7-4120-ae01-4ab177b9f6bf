import cx from 'classnames'
import PropTypes from 'prop-types'
import { PureComponent } from 'react'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'

import { PARAMS } from '../../constants'

import {
  CustomerProfileUI,
  HeaderUI,
  TabContentUI,
} from './CustomerProfile.css'

import {
  customerProfile,
  defaultAvatarUrl,
  permissions,
  selects,
} from '../../selectors'
import ConversationTab from './ConversationTab'
import { withController } from './CustomerProfile.controller'
import EditProfileTab from './EditProfileTab'
import Header from './Header'
import InboxMergeCustomerModal from './InboxMergeCustomerModal'
import CustomerProfileLoading from './Loading'
import MailboxMergeCustomerModal from './MergeProfileModal'
import Navigation from './Navigation'
import NoCustomer from './NoCustomer'

export class CustomerProfile extends PureComponent {
  static propTypes = {
    canUpdate: PropTypes.bool,
    getEmailCustomerUrl: PropTypes.func,
    openUrl: PropTypes.func,
    loaded: PropTypes.bool,
    loading: PropTypes.bool,
    data: PropTypes.shape({
      customer: PropTypes.shape({
        id: PropTypes.string,
        conversations: PropTypes.shape({
          count: PropTypes.number,
        }),
      }),
    }),
    error: PropTypes.string,
    hasMailboxes: PropTypes.bool,
    patchCustomerProfile: PropTypes.func,
    addEmailWithoutMerge: PropTypes.func,
    mergeModalErrorMsg: PropTypes.string,
    cancelMergeModal: PropTypes.func,
    discardProfile: PropTypes.shape({
      email: PropTypes.string,
    }),
    discardProperties: PropTypes.func,
    keeperProfile: PropTypes.object,
    mergeProfiles: PropTypes.func,
    isMergeModalOpen: PropTypes.bool,
    profileId: PropTypes.string,
    tab: PropTypes.string,
    location: PropTypes.object,
    countries: PropTypes.array,
    deleteCustomerProfileEntry: PropTypes.func,
    isCustomerPropertiesEnabled: PropTypes.bool,
    patchCustomerProfileByDelete: PropTypes.func,
    patchCustomerProfileEntry: PropTypes.func,
    patchCustomerProperty: PropTypes.func,
    phones: PropTypes.array,
    preloaded: PropTypes.bool,
    prefetched: PropTypes.bool,
  }

  static defaultProps = {
    cancelMergeModal: () => {},
    countries: [],
    defaultAvatarUrl: '',
    data: { customer: null },
    loaded: false,
    loading: false,
    getEmailCustomerUrl: () => {},
    hasMailboxes: false,
    isCustomerPropertiesEnabled: false,
    openUrl: () => {},
    deleteCustomerProfileEntry: () => {},
    patchCustomerProfile: () => {},
    patchCustomerProfileByDelete: () => {},
    patchCustomerProfileEntry: () => {},
    patchCustomerProperty: () => {},
    phones: [],
    profileId: null,
    tab: PARAMS.PROFILE_TAB_EDIT,
    preloaded: false,
    prefetched: false,
  }

  handleActionOnClick = (e, { mailboxId } = {}) => {
    const { getEmailCustomerUrl, openUrl } = this.props
    const path = getEmailCustomerUrl(mailboxId)
    openUrl(e, path)
  }

  render() {
    const {
      loaded,
      loading,
      preloaded,
      data: { customer },
      prefetched, // This comes from the controller state
    } = this.props

    // If this is a prefetched navigation from CustomerList and we have data,
    // skip the loading state
    if (prefetched && customer && customer.id) {
      return <CustomerProfileUI>{this.renderBody()}</CustomerProfileUI>
    }

    // For non-prefetched navigation (from Company List), show loading state
    if (loading && !loaded) {
      return this.renderLoading()
    }

    if (loaded && !customer && !loading) {
      return this.renderNoCustomer()
    }

    return <CustomerProfileUI>{this.renderBody()}</CustomerProfileUI>
  }

  renderBody = () => {
    const {
      data: { customer },
      loaded,
      loading,
      prefetched,
      error,
    } = this.props

    // For prefetched navigation, prioritize showing content
    if (prefetched && customer) {
      return this.renderContent()
    }

    // For regular navigation, follow normal flow
    if (loading && loaded) {
      return this.renderLoading()
    } else if (error) {
      return this.renderError()
    } else if (customer) {
      return this.renderContent()
    } else {
      return this.renderNoCustomer()
    }
  }

  renderHeader() {
    const {
      canUpdate,
      data: { customer },
      getEmailCustomerUrl,
      hasMailboxes,
      openUrl,
      patchCustomerProfile,
    } = this.props

    return (
      <HeaderUI>
        <Header
          canUpdate={canUpdate}
          customer={customer}
          openUrl={openUrl}
          actionLabel={hasMailboxes ? 'Email Customer' : undefined}
          actionSize="lg"
          getActionUrl={getEmailCustomerUrl}
          patchCustomerProfile={patchCustomerProfile}
        />
      </HeaderUI>
    )
  }

  renderMailboxModal() {
    const {
      addEmailWithoutMerge,
      mergeModalErrorMsg,
      cancelMergeModal,
      discardProfile,
      discardProperties,
      keeperProfile,
      mergeProfiles,
    } = this.props
    return (
      <MailboxMergeCustomerModal
        addEmailWithoutMerge={addEmailWithoutMerge}
        mergeModalErrorMsg={mergeModalErrorMsg}
        cancelMergeModal={cancelMergeModal}
        discardProfile={discardProfile}
        discardProperties={discardProperties}
        keeperProfile={keeperProfile}
        mergeProfiles={mergeProfiles}
      />
    )
  }
  renderInboxModal() {
    const {
      cancelMergeModal,
      discardProfile,
      data: { customer },
      isMergeModalOpen,
    } = this.props

    return (
      <InboxMergeCustomerModal
        isOpen={isMergeModalOpen}
        onClose={cancelMergeModal}
        currentCustomer={customer}
        discardCustomerEmail={discardProfile?.email}
      />
    )
  }

  renderModal() {
    const { isHsAppUiMailboxRewriteEnabled } = window.hsGlobal.features
    return isHsAppUiMailboxRewriteEnabled
      ? this.renderInboxModal()
      : this.renderMailboxModal()
  }

  renderNav() {
    const {
      data: { customer },
      profileId,
      tab,
      location,
    } = this.props
    const navProps = { profileId, tab }
    const totalConversations =
      customer && customer.conversations ? customer.conversations.count : null
    return (
      <Navigation
        {...navProps}
        totalConversations={totalConversations}
        location={location}
      />
    )
  }

  renderEditProfile() {
    const {
      canUpdate,
      countries,
      tab,
      data: { customer },
      deleteCustomerProfileEntry,
      isCustomerPropertiesEnabled,
      patchCustomerProfile,
      patchCustomerProfileByDelete,
      patchCustomerProfileEntry,
      patchCustomerProperty,
      phones,
    } = this.props
    const className = cx(tab == PARAMS.PROFILE_TAB_CONVOS && 'is-hidden')
    return (
      <EditProfileTab
        className={className}
        canUpdate={canUpdate}
        customer={customer}
        countries={countries}
        deleteCustomerProfileEntry={deleteCustomerProfileEntry}
        isCustomerPropertiesEnabled={isCustomerPropertiesEnabled}
        patchCustomerProfile={patchCustomerProfile}
        patchCustomerProfileByDelete={patchCustomerProfileByDelete}
        patchCustomerProfileEntry={patchCustomerProfileEntry}
        patchCustomerProperty={patchCustomerProperty}
        phones={phones}
        showWebsites={true}
      />
    )
  }

  renderConvos() {
    const {
      data: { customer },
      tab,
      profileId,
    } = this.props
    const className = cx(tab != PARAMS.PROFILE_TAB_CONVOS && 'is-hidden')
    return (
      <ConversationTab
        className={className}
        conversations={customer.conversations}
        profileId={profileId}
      />
    )
  }

  renderContent = () => {
    const { isMergeModalOpen } = this.props

    return (
      <div data-cy="CustomerProfile.Content">
        {this.renderHeader()}
        {this.renderNav()}
        {isMergeModalOpen && this.renderModal()}
        <TabContentUI>
          {this.renderEditProfile()}
          {this.renderConvos()}
        </TabContentUI>
      </div>
    )
  }

  renderLoading = () => {
    return <CustomerProfileLoading />
  }

  renderNoCustomer = () => {
    return <NoCustomer />
  }

  renderError = () => {
    const { error } = this.props
    return <p data-cy="CustomerProfile.Error">error when loading {error}</p>
  }
}

export const mapStateToProps = state => {
  const props = customerProfile.resolverSelector(state)
  return {
    ...props,
    defaultAvatarUrl: defaultAvatarUrl.defaultAvatarUrlSelector(state),
    countries: selects.countriesSelector(state),
    phones: selects.phonesSelector(state),
    canUpdate: permissions.canUpdateCustomerSelector(state),
  }
}

export default withRouter(
  withController(connect(mapStateToProps, null)(CustomerProfile))
)
