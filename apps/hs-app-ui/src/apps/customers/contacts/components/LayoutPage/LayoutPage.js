import PropTypes from 'prop-types'
import { Component } from 'react'
import styled from 'styled-components'

import Page from 'hsds/components/page'

export const PageCardUI = styled(Page.Card)`
  min-height: 600px;
`

export class LayoutPage extends Component {
  static propTypes = {
    children: PropTypes.node.isRequired,
    renderBelowCard: PropTypes.func,
  }

  render() {
    const { children, renderBelowCard, ...rest } = this.props

    return (
      <Page {...rest}>
        <PageCardUI>
          <Page.Section>
            <Page.Content>{children}</Page.Content>
          </Page.Section>
        </PageCardUI>
        {renderBelowCard && renderBelowCard()}
      </Page>
    )
  }
}

export default LayoutPage
