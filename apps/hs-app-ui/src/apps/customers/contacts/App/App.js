import React from 'react'
import { Provider as ReduxProvider } from 'react-redux'

import { ContentUI } from './App.css'

import { createStore } from '../store'
import AppProvider from './AppProvider'
import Routes from './Routes'

const {
  features: { isCustomerPropertiesEnabled, isWaitingSinceEnabled },
  memberPermissions: { deleteCustomers, updateCustomers },
} = window.hsGlobal
const { avatarBaseUrl, customer, mailboxes, selects } = window.appData

const preloadedState = {
  basename: 'customers',
  defaultAvatarUrl: `${avatarBaseUrl}01.png`,
  features: {
    // We do not need to store all  the features in memory.
    isCustomerPropertiesEnabled,
    isWaitingSinceEnabled,
  },
  permissions: {
    deleteCustomer: deleteCustomers,
    updateCustomer: updateCustomers,
  },
  mailboxes,
  selects,
  customerProfile: customer
    ? {
        hasLoaded: true,
        isLoading: false,
        isPreloaded: true,
        customer,
      }
    : {
        hasLoaded: false,
        isLoading: false,
        isPreloaded: false,
        customer: { conflicts: [] },
      },
}

// Create the store once, outside of the component
const store = createStore(preloadedState)

export class App extends React.PureComponent {
  render() {
    return (
      <ReduxProvider store={store}>
        <AppProvider>
          <ContentUI className="CustomersApp-content">
            <Routes />
          </ContentUI>
        </AppProvider>
      </ReduxProvider>
    )
  }
}

export default App
