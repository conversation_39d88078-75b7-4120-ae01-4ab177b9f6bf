import { MemoryRouter } from 'react-router-dom'

import { render } from '@testing-library/react'

import { Routes } from './Routes'

// Mock the components used in Routes
jest.mock('../screens/HomeScreen', () => ({
  __esModule: true,
  default: () => <div data-testid="home-screen">Home Screen</div>,
}))

jest.mock('../screens/ProfileScreen', () => ({
  __esModule: true,
  default: () => <div data-testid="profile-screen">Profile Screen</div>,
}))

jest.mock('../screens/NotFoundScreen', () => ({
  __esModule: true,
  default: () => <div data-testid="not-found-screen">Not Found Screen</div>,
}))

describe('Routes', () => {
  test('renders home screen at /customers path', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/customers']}>
        <Routes />
      </MemoryRouter>
    )

    expect(getByTestId('home-screen')).toBeInTheDocument()
  })

  test('renders home screen at /contacts path', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/contacts']}>
        <Routes />
      </MemoryRouter>
    )

    expect(getByTestId('home-screen')).toBeInTheDocument()
  })

  test('renders profile screen at /customers/:id path', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/customers/123']}>
        <Routes />
      </MemoryRouter>
    )

    expect(getByTestId('profile-screen')).toBeInTheDocument()
  })

  test('renders profile screen at /contacts/:id path', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/contacts/123']}>
        <Routes />
      </MemoryRouter>
    )

    expect(getByTestId('profile-screen')).toBeInTheDocument()
  })

  test('renders not found screen for invalid paths', () => {
    const { getByTestId } = render(
      <MemoryRouter initialEntries={['/invalid-path']}>
        <Routes />
      </MemoryRouter>
    )

    expect(getByTestId('not-found-screen')).toBeInTheDocument()
  })
})
