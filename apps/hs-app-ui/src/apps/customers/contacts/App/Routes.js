import React from 'react'
import { Route, Switch } from 'react-router-dom'

import { HomeScreen, NotFoundScreen, ProfileScreen } from '../screens'

export class Routes extends React.Component {
  render() {
    return (
      <Switch>
        <Route exact path="/customers" component={HomeScreen} />
        <Route exact path="/contacts" component={HomeScreen} />
        <Route path="/customers/:id([\w-]+)/:tab?" component={ProfileScreen} />
        <Route path="/contacts/:id([\w-]+)/:tab?" component={ProfileScreen} />
        <Route component={NotFoundScreen} />
      </Switch>
    )
  }
}

export default Routes
