/**
 * Utility functions related to routing in the customers/contacts module
 */

/**
 * Determines if the current path is /contacts or /customers based
 * either on the location prop or window.location
 *
 * @param {Object} [location] - Optional location object from React Router
 * @returns {string} The base path ('/contacts' or '/customers')
 */
export const getCustomersBasePath = location => {
  if (location?.pathname) {
    return location.pathname.startsWith('/contacts')
      ? '/contacts'
      : '/customers'
  }

  // Fallback to window.location if location prop not provided
  return window.location.pathname.startsWith('/contacts')
    ? '/contacts'
    : '/customers'
}
