import { getCustomersBasePath } from './routing'

describe('routing utils', () => {
  describe('getCustomersBasePath', () => {
    const originalLocation = window.location

    beforeEach(() => {
      // Mock window.location
      delete window.location
      window.location = { pathname: '/customers/123' }
    })

    afterEach(() => {
      window.location = originalLocation
    })

    test('returns /customers when location prop has /customers path', () => {
      const location = { pathname: '/customers/123' }
      expect(getCustomersBasePath(location)).toBe('/customers')
    })

    test('returns /contacts when location prop has /contacts path', () => {
      const location = { pathname: '/contacts/123' }
      expect(getCustomersBasePath(location)).toBe('/contacts')
    })

    test('falls back to window.location when no location prop provided', () => {
      expect(getCustomersBasePath()).toBe('/customers')

      window.location = { pathname: '/contacts/123' }
      expect(getCustomersBasePath()).toBe('/contacts')
    })

    test('handles location objects with undefined pathname', () => {
      const location = { search: '?q=test' }
      expect(getCustomersBasePath(location)).toBe('/customers')
    })
  })
})
