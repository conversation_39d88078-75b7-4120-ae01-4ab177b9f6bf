/* eslint-disable no-undef */
import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import LayoutPage from '../../components/LayoutPage'
import { PageCardUI } from '../../components/LayoutPage/LayoutPage'

export const ProfileScreenUI = styled('div')`
  display: block;
`

export const LayoutPageUI = styled(LayoutPage)`
  --hsds-page-margin-bottom: 0;
  position: relative;

  .c-Page {
    z-index: 2;
    position: relative;
  }

  ${PageCardUI} {
    padding: 0;
  }
`

export const ProfileBannerUI = styled('div')`
  background-color: ${getColor('cobalt.300')};
  background-image: url(${hsGlobal.imagePath}profile/profileheader-bg.jpg);
  background-position: center center;
  background-repeat: repeat-x;
  background-size: 900px 90px;
  height: 90px;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;

  @media only screen and (-webkit-min-device-pixel-ratio: 1.25),
    only screen and (min--moz-device-pixel-ratio: 1.25),
    only screen and (-o-min-device-pixel-ratio: 1.25/1),
    only screen and (min-device-pixel-ratio: 1.25),
    only screen and (min-resolution: 200dpi),
    only screen and (min-resolution: 1.25dppx) {
    background-image: url(${hsGlobal.imagePath}profile/<EMAIL>);
  }
`
