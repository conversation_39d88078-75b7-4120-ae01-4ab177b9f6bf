import PropTypes from 'prop-types'
import React from 'react'
import { withRouter } from 'react-router'

import {
  LayoutPageUI,
  ProfileBannerUI,
  ProfileScreenUI,
} from './ProfileScreen.css'

import CustomerProfile from '../../components/CustomerProfile'
import DeleteProfile from '../../components/DeleteProfile'

export class ProfileScreen extends React.PureComponent {
  static propTypes = {
    match: PropTypes.shape({
      params: PropTypes.shape({
        id: PropTypes.string.isRequired,
      }).isRequired,
    }).isRequired,
  }

  componentDidMount() {
    window.scrollTo(0, 0)
  }

  render() {
    const {
      match: {
        params: { id },
      },
    } = this.props

    return (
      <ProfileScreenUI className="ProfileScreenUI">
        <LayoutPageUI
          renderInsideLayout={() => <ProfileBannerUI />}
          fullPage
          renderBelowCard={() => <DeleteProfile maxConversations={100} />}
        >
          <CustomerProfile profileId={id} />
        </LayoutPageUI>
      </ProfileScreenUI>
    )
  }
}

const ProfileScreenWrapper = props => <ProfileScreen {...props} />
ProfileScreenWrapper.displayName = 'ProfileScreenWrapper'

export default withRouter(ProfileScreenWrapper)
