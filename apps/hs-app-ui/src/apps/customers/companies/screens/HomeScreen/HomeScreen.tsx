import { formatNumber } from 'hsds/utils/number'

import { useCompanies } from '../../hooks/useCompanies'

import { SubtitleUI, TitleWrapperUI } from './HomeScreen.css'

import { LayoutPage } from '../../../shared/components'
import { CompanyList } from '../../components/CompanyList/CompanyList'
import { useDemoCreateCompany } from './useDemoCreateCompany'
import Text from 'hsds/components/text'

const HomeScreen = () => {
  useDemoCreateCompany()

  const { pagination } = useCompanies()

  return (
    <>
      <LayoutPage fullPage>
        <TitleWrapperUI>
          <Text size="20" weight={500} role="heading" aria-level={1}>
            Companies
          </Text>
          {pagination.totalItems > 0 && (
            <SubtitleUI weight={400} faint as="span">
              {formatNumber(pagination.totalItems)}
            </SubtitleUI>
          )}
        </TitleWrapperUI>
        <CompanyList />
      </LayoutPage>
    </>
  )
}

export default HomeScreen
