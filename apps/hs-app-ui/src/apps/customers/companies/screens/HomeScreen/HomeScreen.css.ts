import styled from 'styled-components'

import { LayoutPage } from '../../../shared/components/'
import Text from 'hsds/components/text'

export const HomeScreenUI = styled(LayoutPage)``

export const TitleWrapperUI = styled.div`
  margin-bottom: 7px;
  display: flex;
  align-items: baseline;
`

export const SubtitleUI = styled(Text)`
  margin-left: 12px;
  vertical-align: baseline;
  color: var(--hsds-token-text-color-faint);
  font-size: var(--HSDSGlobalTextFontSize-15, 15px);
`
