import { useCallback, useEffect } from 'react'

import { faker } from '@faker-js/faker'
import { OrganizationResponse } from '@helpscout/organization-service-typescript-fetch-client'

import { useCreateCompany } from '../../hooks/useCreateCompany'

declare global {
  interface Window {
    _createDemoCompany?: () => Promise<OrganizationResponse>
  }
}

export const useDemoCreateCompany = () => {
  const createCompanyMutation = useCreateCompany()

  const demoHandleCreateCompany = useCallback(async () => {
    const randomName = faker.name.firstName()
    const newOrganization = await createCompanyMutation.mutateAsync({
      name: `TESTTEST ${randomName}`,
      website: `testtest${randomName.toLowerCase()}.com`,
      location: 'US',
    })
    console.log(newOrganization)
    return newOrganization
  }, [createCompanyMutation])

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      window._createDemoCompany = demoHandleCreateCompany
    }
  }, [demoHandleCreateCompany])
}
