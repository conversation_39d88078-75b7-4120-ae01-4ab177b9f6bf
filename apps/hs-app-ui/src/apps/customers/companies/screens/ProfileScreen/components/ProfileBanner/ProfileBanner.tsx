import { useHistory, useLocation } from 'react-router'
import { useParams } from 'react-router-dom'

import { useCompany } from '../../../../hooks/useCompany'

import { ButtonIconUI, ProfileBannerUI } from './ProfileBanner.css'

import ArrowLeft from 'hsds/icons/arrow-left'

const ProfileBanner = () => {
  const { id } = useParams<{ id: string }>()
  const { data: company } = useCompany(id)
  const history = useHistory()
  const location = useLocation<{ fromQuery?: string }>()

  const handleBackToCompanies = () => {
    history.push(`/companies${location.state?.fromQuery || ''}`)
  }

  // we can use the originalbrand color only if the logo is set
  const brandColor: string | undefined = company?.logoUrl
    ? company?.brandColor
    : undefined

  return (
    <ProfileBannerUI
      brandColor={brandColor}
      defaultBrandColorName={company?.defaultBrandColorName || ''}
    >
      <ButtonIconUI
        icon={ArrowLeft}
        size="lg"
        color="white"
        aria-label="Back"
        onClick={handleBackToCompanies}
      />
    </ProfileBannerUI>
  )
}

export { ProfileBanner }
