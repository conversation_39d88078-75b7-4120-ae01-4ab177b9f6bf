import { useParams } from 'react-router-dom'

import { LayoutPageUI, ProfileScreenUI } from './ProfileScreen.css'

import { CompanyProfile } from '../../App/components/CompanyProfile'
import { ProfileBanner } from './components/ProfileBanner'

const ProfileScreen: React.FC = () => {
  const { id } = useParams<{ id: string }>()

  return (
    <ProfileScreenUI>
      <LayoutPageUI fullPage renderInsideLayout={() => <ProfileBanner />}>
        <CompanyProfile companyId={id} />
      </LayoutPageUI>
    </ProfileScreenUI>
  )
}

export { ProfileScreen }
