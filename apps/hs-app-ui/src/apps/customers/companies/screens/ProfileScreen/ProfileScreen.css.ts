import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import { LayoutPage } from '../../../shared/components'

export const ProfileScreenUI = styled('div')`
  display: block;
`

export const LayoutPageUI = styled(LayoutPage)`
  position: relative;
  --hsds-page-card-padding-top: 10px;
  --hsds-page-card-padding-bottom: 10px;
  --hsds-page-card-padding-side: 10px;
  --hsds-page-margin-top: 40px;
  background-color: ${getColor('charcoal.100')};

  .c-Page {
    z-index: 2;
    position: relative;
  }
`
