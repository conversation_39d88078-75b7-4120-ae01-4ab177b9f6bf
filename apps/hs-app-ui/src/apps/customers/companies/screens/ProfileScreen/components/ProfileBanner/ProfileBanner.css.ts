import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import ButtonIcon from 'hsds/components/button-icon'

interface ProfileBannerUIProps {
  brandColor?: string
  defaultBrandColorName: string
}

export const ProfileBannerUI = styled.div<ProfileBannerUIProps>`
  /* Fallback background color */
  background-color: ${({ brandColor, defaultBrandColorName }) =>
    brandColor
      ? brandColor
      : getColor(`pastel.light.${defaultBrandColorName}`)};

  /* If color-mix is supported, override with color-mix */
  @supports (background-color: color-mix(in srgb, red, blue)) {
    background-color: ${({ brandColor, defaultBrandColorName }) =>
      brandColor
        ? brandColor
        : `color-mix(in srgb, ${getColor(
            `pastel.light.${defaultBrandColorName}`
          )} 50%, transparent)`};
  }
  width: 100%;
  height: 295px;
  background-size: 900px 90px;
  position: absolute;
  overflow: hidden;
  left: 0;
  right: 0;
  top: 0;
`
export const ButtonIconUI = styled(ButtonIcon)`
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 3;
`
