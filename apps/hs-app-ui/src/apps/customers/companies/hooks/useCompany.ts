import { OrganizationResponse } from '@helpscout/organization-service-typescript-fetch-client'

import { getFallbackCompanyColorName } from 'shared/utils/companies/color'
import { trpc } from 'shared/utils/trpc'

import { Company } from '../types/companies'

export const useCompany = (companyId: string) => {
  const query = trpc.companies.getOrganization.useQuery(
    {
      id: parseInt(companyId),
      enriched: true,
    },
    {
      select: (
        data: OrganizationResponse & {
          customerCount?: number
          conversationCount?: number
        }
      ): Company => {
        const defaultBrandColorName = getFallbackCompanyColorName(data.id)

        return {
          ...data,
          conversationCount: data.conversationCount || 0,
          customerCount: data.customerCount || 0,
          defaultBrandColorName,
        } as Company
      },
    }
  )

  return query
}
