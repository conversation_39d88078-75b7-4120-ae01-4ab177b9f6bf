import type { SaveOrganizationRequest } from '@helpscout/organization-service-typescript-fetch-client'

import { trpc } from 'shared/utils/trpc'

import { useNoty } from 'shared/hooks'

type SaveCompanyParams = {
  organizationId: number
  organization: SaveOrganizationRequest
}

export const useSaveCompany = () => {
  const { showSuccessNoty, showErrorNoty } = useNoty()
  const utils = trpc.useUtils()

  const mutation = trpc.companies.saveOrganization.useMutation({
    onSuccess: async (_, variables) => {
      // Invalidate the company query to trigger a refetch for the company details
      // This ensures that the company details in the header are updated
      // Invalidate both enriched and non-enriched versions
      await Promise.all([
        utils.companies.getOrganization.invalidate({
          id: variables.organizationId,
          enriched: true,
        }),
        utils.companies.getOrganization.invalidate({
          id: variables.organizationId,
          enriched: false,
        }),
      ])
      showSuccessNoty('Company updated successfully')
    },
    onError: () => {
      showErrorNoty('Failed to update company')
    },
  })

  return {
    saveCompany: (params: SaveCompanyParams) => mutation.mutateAsync(params),
    isLoading: mutation.isLoading,
    isError: mutation.isError,
    error: mutation.error,
  }
}
