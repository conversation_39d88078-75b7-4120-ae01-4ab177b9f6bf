import { useEffect, useRef, useState } from 'react'
import { useHistory, useLocation } from 'react-router-dom'

import { trpc } from 'shared/utils/trpc'

export type UseConversationsOptions = {
  companyId?: number
  initialPage?: number
  initialPageLimit?: number
}

export const useConversations = (options: UseConversationsOptions) => {
  const location = useLocation()
  const history = useHistory()

  // Calculate initial page value
  const getInitialPageValue = () => {
    const searchParams = new URLSearchParams(location.search)
    const pageParam = searchParams.get('page')
    return pageParam ? parseInt(pageParam, 10) : options?.initialPage || 1
  }

  // Store initial page in a ref to avoid re-parsing on re-renders
  const initialPageRef = useRef(getInitialPageValue())

  const [page, setPage] = useState(initialPageRef.current)
  const [pageLimit, setPageLimit] = useState(options?.initialPageLimit || 20)

  const isChangingPage = useRef(false)

  useEffect(() => {
    if (isChangingPage.current) {
      isChangingPage.current = false
      return
    }

    const searchParams = new URLSearchParams(location.search)
    const pageParam = searchParams.get('page')
    const currentPage = pageParam
      ? parseInt(pageParam, 10)
      : options?.initialPage || 1

    if (page !== currentPage) {
      setPage(currentPage)
    }
  }, [location.search, options?.initialPage, page])

  const updateUrl = (newPage: number) => {
    const searchParams = new URLSearchParams(location.search)

    if (newPage === 1) {
      searchParams.delete('page')
    } else {
      searchParams.set('page', newPage.toString())
    }

    const newSearch = searchParams.toString()
    const queryString = newSearch ? `?${newSearch}` : ''

    if (location.search !== queryString) {
      isChangingPage.current = true
      history.replace({
        pathname: location.pathname,
        search: queryString,
      })
    }
  }

  const query = trpc.companies.listConversationsByCompany.useQuery(
    { companyId: options.companyId as number, page, limit: pageLimit },
    {
      // query will only run when companyId is truthy
      enabled: !!options.companyId,
      select: data => {
        if (!data) {
          return undefined
        }
        // Handle the case where data.items is undefined or null
        if (!data.items) {
          return {
            ...data,
            items: [],
          }
        }
        return data
      },
    }
  )

  const goToPage = (newPage: number) => {
    setPage(newPage)
    updateUrl(newPage)
  }

  const setItemsPerPage = (newPageLimit: number) => {
    setPageLimit(newPageLimit)
    setPage(1)
    updateUrl(1)
  }

  return {
    data: query.data,
    isLoading: query.isLoading,
    pagination: {
      page,
      pageLimit,
      totalPages: query.data?.pages || 0,
      totalItems: query.data?.count || 0,
      goToPage,
      setItemsPerPage,
    },
  }
}
