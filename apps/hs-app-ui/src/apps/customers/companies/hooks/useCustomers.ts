import { useEffect, useRef, useState } from 'react'
import { useHistory, useLocation } from 'react-router-dom'

import { generateFallbackAvatarUrl } from 'shared/utils/generateFallbackAvatarUrl'
import { trpc } from 'shared/utils/trpc'

import { useHsAppContext } from 'shared/hooks/useHsAppContext'

export type UseCustomersOptions = {
  organizationId?: number
  initialPage?: number
  initialPageLimit?: number
}

export const useCustomers = (options: UseCustomersOptions) => {
  const location = useLocation()
  const history = useHistory()

  // Parse page from URL query param or use default
  const getInitialPage = (): number => {
    const searchParams = new URLSearchParams(location.search)
    const pageParam = searchParams.get('page')
    return pageParam ? parseInt(pageParam, 10) : options?.initialPage || 1
  }

  const [page, setPage] = useState(getInitialPage())
  const [pageLimit, setPageLimit] = useState(options?.initialPageLimit || 20)

  const {
    appData: { avatarBaseUrl },
  } = useHsAppContext()

  const isChangingPage = useRef(false)

  useEffect(() => {
    if (isChangingPage.current) {
      isChangingPage.current = false
      return
    }

    const currentPage = getInitialPage()
    if (page !== currentPage) {
      setPage(currentPage)
    }
  }, [location.search])

  const updateUrl = (newPage: number) => {
    const searchParams = new URLSearchParams(location.search)

    if (newPage === 1) {
      searchParams.delete('page')
    } else {
      searchParams.set('page', newPage.toString())
    }

    const newSearch = searchParams.toString()
    const queryString = newSearch ? `?${newSearch}` : ''

    if (location.search !== queryString) {
      isChangingPage.current = true
      history.replace({
        pathname: location.pathname,
        search: queryString,
      })
    }
  }

  const query = trpc.companies.listCustomersByOrganization.useQuery(
    {
      organizationId: options.organizationId!,
      page,
      limit: pageLimit,
    },
    {
      // prevent query from running if organizationId is undefined
      enabled: options.organizationId !== undefined,
      select: data => {
        if (!data) {
          return undefined
        }

        // Handle the case where data.items is undefined or null
        if (!data.items) {
          return {
            ...data,
            items: [],
          }
        }

        const dataWithAvatars = {
          ...data,
          items: data.items.map(item => {
            const itemFallbackAvatarUrl =
              generateFallbackAvatarUrl(avatarBaseUrl)
            return {
              ...item,
              defaultAvatarUrl: itemFallbackAvatarUrl,
            }
          }),
        }

        return dataWithAvatars
      },
    }
  )

  const goToPage = (newPage: number) => {
    setPage(newPage)
    updateUrl(newPage)
  }

  const setItemsPerPage = (newPageLimit: number) => {
    setPageLimit(newPageLimit)
    setPage(1)
    updateUrl(1)
  }

  return {
    data: query.data,
    isLoading: query.isLoading,
    pagination: {
      page,
      pageLimit,
      totalPages: query.data?.pages || 0,
      totalItems: query.data?.count || 0,
      goToPage,
      setItemsPerPage,
    },
  }
}
