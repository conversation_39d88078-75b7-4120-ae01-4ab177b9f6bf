import { useEffect, useState } from 'react'
import { useHistory, useLocation } from 'react-router-dom'

import { generateFallbackAvatarUrl } from 'shared/utils/generateFallbackAvatarUrl'
import { trpc } from 'shared/utils/trpc'

import { useHsAppContext } from 'shared/hooks/useHsAppContext'

interface ProcessedCompanyData<TItem = any> {
  items: Array<TItem & { defaultAvatarUrl: string }>
  count: number
  pages: number
  nextPage?: number | null
  prevPage?: number | null
}

export type UseCompaniesOptions = {
  initialPage?: number
  initialPageSize?: number
  initialSearchTerm?: string // Optional: if passed, overrides URL initially
}

const processCompanyData = (
  data:
    | {
        items?: any[]
        count?: number
        pages?: number
        nextPage?: number | null
        prevPage?: number | null
      }
    | undefined,
  avatarBaseUrl: string
): ProcessedCompanyData | undefined => {
  if (!data) {
    return undefined
  }
  const items = data.items || []
  const processedItems = items.map(item => ({
    ...item,
    defaultAvatarUrl: generateFallbackAvatarUrl(avatarBaseUrl),
  }))
  return {
    items: processedItems,
    count: data.count || 0,
    pages: data.pages || 0,
    nextPage: data.nextPage,
    prevPage: data.prevPage,
  }
}

const updateUrl = (
  location: ReturnType<typeof useLocation>,
  history: ReturnType<typeof useHistory>,
  newPage: number,
  newSearchTerm: string | null = ''
) => {
  const searchParams = new URLSearchParams(location.search)

  if (newPage === 1) {
    searchParams.delete('page')
  } else {
    searchParams.set('page', newPage.toString())
  }

  if (newSearchTerm && newSearchTerm.trim() !== '') {
    searchParams.set('query', newSearchTerm)
  } else {
    searchParams.delete('query')
  }

  const newSearchQueryString = searchParams.toString()
  const newPath =
    location.pathname + (newSearchQueryString ? `?${newSearchQueryString}` : '')
  const oldPath = location.pathname + location.search

  if (oldPath !== newPath) {
    history.replace({
      pathname: location.pathname,
      search: newSearchQueryString ? `?${newSearchQueryString}` : '',
    })
  }
}

export const useCompanies = (options?: UseCompaniesOptions) => {
  const location = useLocation()
  const history = useHistory()
  const {
    appData: { avatarBaseUrl },
  } = useHsAppContext()

  const searchParams = new URLSearchParams(location.search)
  const page = searchParams.get('page')
    ? parseInt(searchParams.get('page')!, 10)
    : options?.initialPage || 1
  const searchTerm =
    searchParams.get('query') || options?.initialSearchTerm || ''
  const [pageSize, setPageSize] = useState(options?.initialPageSize || 50)

  const listQuery = trpc.companies.listOrganizations.useQuery(
    {
      page,
      pageSize,
    },
    {
      enabled: !searchTerm,
      select: data => processCompanyData(data, avatarBaseUrl),
    }
  )

  const [searchQueryData, setSearchQueryData] = useState<
    ProcessedCompanyData | undefined
  >(undefined)
  const [isSearchQueryLoading, setIsSearchQueryLoading] = useState(false)
  const [searchQueryError, setSearchQueryError] = useState<Error | null>(null)

  const autocompleteQuery =
    trpc.companies.autocompleteOrganizationsWithCounts.useQuery(searchTerm, {
      enabled: !!searchTerm,
      select: data => data || [],
    })

  useEffect(() => {
    if (searchTerm) {
      setIsSearchQueryLoading(autocompleteQuery.isLoading)
      setSearchQueryError(autocompleteQuery.error as Error | null)
      const allItems = autocompleteQuery.data || []
      setSearchQueryData(
        processCompanyData(
          {
            items: allItems,
            count: allItems.length,
            pages: 1,
            nextPage: null,
            prevPage: null,
          },
          avatarBaseUrl
        )
      )
    } else {
      setSearchQueryData(undefined)
      setSearchQueryError(null)
      setIsSearchQueryLoading(false)
    }
  }, [
    searchTerm,
    avatarBaseUrl,
    autocompleteQuery.data,
    autocompleteQuery.isLoading,
    autocompleteQuery.error,
  ])

  const goToPage = (newPage: number) => {
    updateUrl(location, history, newPage, searchTerm)
  }

  const setItemsPerPage = (newPageSize: number) => {
    const oldPageSize = pageSize
    setPageSize(newPageSize)
    const firstItemIndex = (page - 1) * oldPageSize
    const newPage = Math.floor(firstItemIndex / newPageSize) + 1
    updateUrl(location, history, newPage, searchTerm)
  }

  const setSearch = (newSearchTerm: string) => {
    updateUrl(location, history, 1, newSearchTerm.trim())
  }

  const isLoading = searchTerm ? isSearchQueryLoading : listQuery.isLoading
  const data = searchTerm ? searchQueryData : listQuery.data
  const error = searchTerm ? searchQueryError : listQuery.error
  const currentQuery = {
    isLoading,
    data,
    error,
  }

  const showLoading = isLoading && !data?.items?.length && !error
  const showError = !!error
  const showNoResultsForSearch =
    !isLoading && !error && data?.items?.length === 0 && !!searchTerm
  const showNoCompaniesAvailable =
    !isLoading && !error && data?.items?.length === 0 && !searchTerm
  const showTable = !error && !!data?.items && data.items.length > 0
  const showPagination =
    showTable && !!data && (data.pages || 0) > 1 && (data.count || 0) > pageSize

  return {
    ...currentQuery,
    currentSearchTerm: searchTerm,
    setSearchTerm: setSearch,
    pagination: {
      page,
      pageSize,
      totalPages: currentQuery.data?.pages || 0,
      totalItems: currentQuery.data?.count || 0,
      goToPage,
      setItemsPerPage,
    },
    showLoading,
    showError,
    showNoResultsForSearch,
    showNoCompaniesAvailable,
    showTable,
    showPagination,
  }
}

export type UseCompaniesReturn = ReturnType<typeof useCompanies>
