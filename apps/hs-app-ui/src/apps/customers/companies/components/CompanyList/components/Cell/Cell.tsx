/* eslint-disable react/display-name */
import cx from 'classnames'
import React from 'react'

import { getCacheBustedUrl } from '../../../../utils'
import { formatNumber } from 'hsds/utils/number'

import {
  AvatarUI,
  BadgeCountUI,
  ButtonUI,
  CellHeaderTextUI,
  FlexyBlockUI,
  IconHeaderUI,
  PrimaryTextUI,
  SkeletonAvatarUI,
  SkeletonTextUI,
  SortIconUI,
} from './Cell.css'

import Flexy from 'hsds/components/flexy'
import VisuallyHidden from 'hsds/components/visually-hidden'
import type { IconType } from 'hsds/icons/baseIcon'
import CaretDown from 'hsds/icons/caret-down'
import CaretUp from 'hsds/icons/caret-up'

export const SORT_ORDER_DESCENDING = 'desc'

type CellProps = {
  [key: string]: any // Allow any other props passed down by the table
}

type CountCellProps = CellProps & {
  customerCount?: number
  conversationCount?: number
}

type GetHeaderCellOptions = {
  icon?: IconType
  sortCallback?: (sortOn: string | boolean, sortedInfo: SortedInfo) => void
  sortOn?: string | boolean
}

type HeaderCellInnerProps = {
  title?: string
}

type SortedInfo = {
  columnKey?: string | boolean
  order?: string
}

type GetSkeletonCellOptions = {
  withAvatar?: boolean
}

type AvatarSize =
  | 'xs'
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | 'xxl'
  | 'xxxl'
  | 'xxxxl'
  | 'xxs'
  | 'smmd'

type SkeletonCellInnerProps = {
  size?: AvatarSize
  width?: string
}

type NameCellProps = CellProps & {
  customerName?: string
  customerEmails?: string[]
  jobTitle?: string
  name?: string
  organization?: string
  logoUrl?: string
  defaultAvatarUrl?: string
  type?: string
}

export const CountCell: React.FC<CountCellProps> = ({
  customerCount,
  conversationCount,
} = {}) => {
  const count = customerCount ?? conversationCount

  const formattedCount = typeof count === 'number' ? formatNumber(count) : '0'
  return <BadgeCountUI>{formattedCount}</BadgeCountUI>
}

export const getHeaderCell = ({
  icon,
  sortCallback = () => {},
  sortOn = false,
}: GetHeaderCellOptions = {}) => {
  return (
    { title }: HeaderCellInnerProps = {},
    sortedInfo: SortedInfo = {}
  ): JSX.Element => {
    const innerContent = icon ? (
      <span>
        <VisuallyHidden>{title}</VisuallyHidden>
        <IconHeaderUI
          data-testid="IconHeaderUI"
          size="24"
          icon={icon}
          data-cy="CustomerList.Header.IconHeader"
        />
      </span>
    ) : (
      <span>{title}</span>
    )
    const isCurrentSortColumn = sortOn === sortedInfo.columnKey

    let direction: 'up' | 'down'
    if (!isCurrentSortColumn) {
      direction = 'up'
    } else {
      direction = sortedInfo.order === SORT_ORDER_DESCENDING ? 'down' : 'up'
    }

    const weight = isCurrentSortColumn ? 700 : 500
    const classNames = cx(
      icon && 'is-icon-only',
      !isCurrentSortColumn && 'is-hidden'
    )

    const content = (
      <CellHeaderTextUI weight={weight}>
        {innerContent}
        <SortIconUI
          data-testid="SortIconUI"
          className={classNames}
          icon={direction === 'up' ? CaretUp : CaretDown}
          size="14"
          data-cy="CustomerList.Header.SortIcon"
        />
      </CellHeaderTextUI>
    )

    if (!sortOn) {
      return content
    }

    return (
      <ButtonUI
        color="grey"
        linked
        inlined
        onClick={() => {
          if (typeof sortOn === 'string' || typeof sortOn === 'boolean') {
            // Type guard
            sortCallback(sortOn, sortedInfo)
          }
        }}
      >
        {content}
      </ButtonUI>
    )
  }
}

export const getSkeletonCell =
  ({ withAvatar = false }: GetSkeletonCellOptions = {}) =>
  ({
    size = 'sm',
    width = '100%',
  }: SkeletonCellInnerProps = {}): JSX.Element => {
    const text = <SkeletonTextUI data-testid="SkeletonTextUI" width={width} />

    if (!withAvatar) {
      return text
    }

    return (
      <Flexy just="center">
        <Flexy.Item>
          <SkeletonAvatarUI data-testid="SkeletonAvatarUI" size={size} />
        </Flexy.Item>
        <FlexyBlockUI>{text}</FlexyBlockUI>
      </Flexy>
    )
  }
export const NameCell: React.FC<NameCellProps> = ({
  name,
  logoUrl: image,
  defaultAvatarUrl,
} = {}) => {
  return (
    <Flexy just="center">
      <Flexy.Item>{renderAvatar(image, defaultAvatarUrl)}</Flexy.Item>
      <FlexyBlockUI>{renderName(name)}</FlexyBlockUI>
    </Flexy>
  )
}

function renderAvatar(image?: string, defaultAvatarUrl?: string): JSX.Element {
  return (
    <AvatarUI
      image={getCacheBustedUrl(image || defaultAvatarUrl)}
      size="sm"
      fallbackImage={defaultAvatarUrl}
    />
  )
}

function renderName(name?: string, email?: string): JSX.Element | null {
  if (!name || name === email) {
    // Check if name exists
    return null
  }
  return (
    <PrimaryTextUI
      data-testid="PrimaryTextUI"
      data-cy="Name.Name"
      size={14}
      weight={500}
    >
      {name}
    </PrimaryTextUI>
  )
}
