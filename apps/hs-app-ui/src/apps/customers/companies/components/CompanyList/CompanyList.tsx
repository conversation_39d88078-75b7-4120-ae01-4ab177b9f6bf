import { useHistory } from 'react-router'

import { useCompanies } from '../../hooks/useCompanies'

import { PaginationUI, SearchContainerUI } from './CompanyList.css'

import { skin } from './CompanyList.constants'
import { Loading } from './components/Loading/Loading'
import { SearchBar } from './components/SearchBar/SearchBar'
import { tableConfig } from './table.config'
import { EmptyList } from 'apps/customers/shared/components/EmptyList/EmptyList'
import { ListError } from 'apps/customers/shared/components/ListError/ListError'
import { NoResults } from 'apps/customers/shared/components/NoResults/NoResults'
import Table from 'hsds/components/table'

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
const CompanyTable: any = Table

export const CompanyList = () => {
  const {
    isLoading,
    pagination,
    data: companyData,
    currentSearchTerm,
    setSearchTerm,
    showLoading,
    showError,
    showNoResultsForSearch,
    showNoCompaniesAvailable,
    showTable,
    showPagination,
  } = useCompanies()
  const history = useHistory()

  const handleRowClick = (
    event: React.MouseEvent<HTMLTableRowElement>,
    row: { id: number }
  ) => {
    if (event.ctrlKey || event.metaKey) {
      window.open(`/companies/${row?.id}`, '_blank')
      return
    }

    history.push({
      pathname: `/companies/${row?.id}`,
      state: { fromQuery: location.search },
    })
  }

  return (
    <>
      <SearchContainerUI>
        <SearchBar
          onSearch={setSearchTerm}
          initialSearchTerm={currentSearchTerm || ''}
          placeholder="Search companies..."
        />
      </SearchContainerUI>
      {showError && <ListError />}
      {showLoading && <Loading />}
      {showNoResultsForSearch && <NoResults query={currentSearchTerm} />}
      {showNoCompaniesAvailable && (
        <EmptyList
          title="No companies yet"
          message="Fear not though! As soon as companies are identified, you'll see them listed here."
        />
      )}
      {showTable && (
        <CompanyTable
          columnChooserResetLabel="Reset"
          isLoading={isLoading}
          columns={tableConfig().columns}
          data={companyData?.items || []}
          withTallRows={true}
          withFocusableRows
          tableDescription="Customers List"
          onRowClick={handleRowClick}
          skin={skin}
        />
      )}
      {showPagination && (
        <PaginationUI
          activePage={pagination.page}
          totalItems={pagination.totalItems}
          rangePerPage={pagination.pageSize}
          onChange={pagination.goToPage}
        />
      )}
    </>
  )
}
