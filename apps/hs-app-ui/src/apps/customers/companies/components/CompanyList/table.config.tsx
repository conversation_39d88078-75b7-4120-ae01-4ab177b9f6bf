import { PrimaryTextUI } from './components/Cell/Cell.css'

import {
  Count<PERSON>ell,
  NameCell,
  getHeaderCell,
  getSkeletonCell,
} from './components/Cell/Cell'

export const tableConfig = (isSkeleton = false) => {
  const columns = [
    {
      title: 'Companies',
      columnKey: ['name', 'logoUrl', 'defaultAvatarUrl'],
      renderCell: isSkeleton
        ? () => getSkeletonCell({ withAvatar: true })({ width: '53%' })
        : (props: Record<string, any>) => <NameCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '35%',
    },
    {
      title: 'Domain',
      columnKey: 'domain',
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '15%' })
        : (props: { domain?: string }) => (
            <PrimaryTextUI>{props.domain}</PrimaryTextUI>
          ),
      renderHeaderCell: getHeaderCell(),
      width: '35%',
    },
    {
      title: 'Conversations',
      columnKey: 'conversationCount',
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '100px' })
        : (props: Record<string, any>) => <CountCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '15%',
    },
    {
      title: 'Contacts',
      columnKey: 'customerCount',
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '28%' })
        : (props: Record<string, any>) => <CountCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '15%',
    },
  ]

  if (isSkeleton) {
    return {
      columns,
      data: getSkeletonData(),
    }
  }

  return { columns }
}

export const getSkeletonData = () => [
  {
    id: 'skeleton-1',
    name: '70%',
    customerCount: '50%',
    conversationCount: '100%',
    domain: '50%',
  },
  {
    id: 'skeleton-2',
    name: '80%',
    customerCount: '50%',
    conversationCount: '50%',
    domain: '50%',
  },
  {
    id: 'skeleton-3',
    name: '50%',
    customerCount: '50%',
    conversationCount: '50%',
    domain: '50%',
  },
  {
    id: 'skeleton-4',
    name: '70%',
    customerCount: '50%',
    conversationCount: '50%',
    domain: '50%',
  },
]
