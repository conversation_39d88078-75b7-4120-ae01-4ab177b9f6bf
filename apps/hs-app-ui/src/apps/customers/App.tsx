import { ComponentProps, ComponentType } from 'react'
import {
  Redirect,
  Route,
  BrowserRouter as Router,
  Switch,
  useLocation,
} from 'react-router-dom'
import styled from 'styled-components'

import { parseCompanyUrl } from './utils'

import CompaniesApp from './companies/App'
import ContactsApp from './contacts/App'
import TabBar from 'hsds/components/tab-bar'
import HsApp from 'shared/components/HsApp'

type HsAppProps = ComponentProps<typeof HsApp>

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const HsAppComponent: ComponentType<any> = (props: HsAppProps) => (
  <HsApp {...props} />
)

const SwitcherContainer = styled.div`
  width: 100%;
`

const AppContainer = styled.div`
  width: 100%;
`

// eslint-disable-next-line unused-imports/no-unused-vars, @typescript-eslint/no-unused-vars
const Navigation = () => {
  const location = useLocation()
  const { id } = parseCompanyUrl(location.pathname)
  const isCreateCompany = location.pathname.includes('/companies/create')
  const isCompanyProfile = Boolean(id)

  if (isCompanyProfile || isCreateCompany) {
    return null
  }

  return (
    <SwitcherContainer>
      <TabBar withReactRouter variant="border-bottom">
        <TabBar.Item to="/customers">Contacts</TabBar.Item>
        <TabBar.Item to="/companies">Companies</TabBar.Item>
      </TabBar>
    </SwitcherContainer>
  )
}

const App = () => {
  return (
    <HsAppComponent isFullscreen={false} role="main">
      <Router>
        <AppContainer>
          {/* we're hiding the navigation for now! */}
          {/* <Navigation /> */}
          <Switch>
            <Route path="/customers" component={ContactsApp} />
            <Route path="/contacts" component={ContactsApp} />
            <Route path="/companies" component={CompaniesApp} />
            <Redirect exact from="/" to="/customers" />
          </Switch>
        </AppContainer>
      </Router>
    </HsAppComponent>
  )
}

export default App
