import React from 'react'
import styled from 'styled-components'

import Page from 'hsds/components/page'

export const PageCardUI = styled(Page.Card)`
  min-height: 600px;
`

const LayoutPage = (props: {
  fullPage?: boolean
  children: React.ReactNode
  renderInsideLayout?: () => React.ReactNode
}) => {
  const { children, fullPage, ...rest } = props

  return (
    <Page fullPage={fullPage} {...rest}>
      <PageCardUI>
        <Page.Section>
          <Page.Content>{children}</Page.Content>
        </Page.Section>
      </PageCardUI>
    </Page>
  )
}

export { LayoutPage }
