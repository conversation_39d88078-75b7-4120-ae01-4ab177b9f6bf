import PropTypes from 'prop-types'
import { ComponentType } from 'react'

import { useGlobals } from 'shared/components/HsApp/HsApp.utils'
import { openBeaconArticle } from 'shared/utils/beacon'

import {
  ButtonUI,
  ExampleImageUI,
  ListIconUI,
  ListItemUI,
  ListUI,
  PageUI,
} from './BlankSlate.css'

import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import Checkmark from 'hsds/icons/check'

type TitleProps = {
  headingLevel: string
}

type HeaderProps = {
  Subtitle: ComponentType
  Title: ComponentType<TitleProps>
}

type Props = {
  beaconArticleId?: string
  children?: React.ReactNode
  exampleImageProps?: React.ComponentProps<typeof ExampleImageUI>
  featuresList?: string[]
  onPrimaryCallToActionClick: () => void
  primaryCallToActionText: string
  shouldShowCallToActionButtons?: boolean
  subtitle?: React.ReactNode
  title: React.ReactNode
}

export const BlankSlate = ({
  beaconArticleId,
  children,
  exampleImageProps,
  featuresList,
  onPrimaryCallToActionClick,
  primaryCallToActionText,
  shouldShowCallToActionButtons = true,
  subtitle,
  title,
}: Props) => {
  const { imagePath } = useGlobals()

  const handleLearnMoreClick = () => {
    if (beaconArticleId) {
      openBeaconArticle(beaconArticleId)
    }
  }
  return (
    <PageUI
      data-testid="BlankSlate"
      backgroundImagePath={`${imagePath}inbox-settings/brush-stroke-background.png`}
    >
      <Page.Card>
        <Page.Section>
          <Page.Header
            render={({ Title, Subtitle }: HeaderProps) => {
              return (
                <>
                  <Title headingLevel="h1">{title}</Title>
                  <Subtitle>{subtitle}</Subtitle>
                </>
              )
            }}
          />
          <Page.Content>
            {featuresList && (
              <ListUI>
                {featuresList.map((feature, index) => (
                  <ListItemUI key={index}>
                    <ListIconUI icon={Checkmark} />
                    {feature}
                  </ListItemUI>
                ))}
              </ListUI>
            )}
            {shouldShowCallToActionButtons && (
              <>
                <ButtonUI onClick={onPrimaryCallToActionClick} color="blue">
                  {primaryCallToActionText}
                </ButtonUI>
                <Button onClick={handleLearnMoreClick} linked>
                  Learn more
                </Button>
              </>
            )}
            {children}
            <ExampleImageUI {...exampleImageProps} />
          </Page.Content>
        </Page.Section>
      </Page.Card>
    </PageUI>
  )
}

BlankSlate.propTypes = {
  beaconArticleId: PropTypes.string,
  children: PropTypes.node,
  exampleImageProps: PropTypes.shape({
    src: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
  }).isRequired,
  featuresList: PropTypes.array.isRequired,
  onPrimaryCallToActionClick: PropTypes.func,
  primaryCallToActionText: PropTypes.string,
  shouldShowCallToActionButtons: PropTypes.bool,
  subtitle: PropTypes.node.isRequired,
  title: PropTypes.node.isRequired,
}
