import cx from 'classnames'
import { ComponentType } from 'react'

import {
  ButtonUI,
  HeaderWrapperUI,
  PageHeaderUI,
} from './HeaderWithSearchFilter.css'

import Tooltip from 'hsds/components/tooltip'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import { SearchBar } from 'shared/components/SearchBar/SearchBar'

type TitleProps = {
  headingLevel: string
}

type HeaderProps = {
  Subtitle: ComponentType
  Title: ComponentType<TitleProps>
}

export function HeaderWithSearchFilter({
  onSearch,
  title,
  subtitle,
  onButtonClick,
  buttonLabel,
  searchAriaLabel,
  buttonDisabled = false,
  buttonDisabledTooltip = '',
  className,
  dataCy = 'SearchBar',
  shouldShowSearch = true,
}: {
  onSearch: (searchQuery: string) => void
  title: React.ReactNode
  subtitle: React.ReactNode
  onButtonClick: () => void
  buttonLabel: string
  searchAriaLabel: string
  buttonDisabled?: boolean
  buttonDisabledTooltip?: string
  className?: string
  dataCy?: string
  shouldShowSearch?: boolean
}) {
  return (
    <PageHeaderUI
      className={cx(className, { 'no-search': !shouldShowSearch })}
      withBorder={false}
      render={({ Title, Subtitle }: HeaderProps) => {
        return (
          <>
            <HeaderWrapperUI
              data-cy="HeaderWithSearchFilter"
              className={cx({ 'has-search': shouldShowSearch })}
            >
              <div>
                <Title headingLevel="h2">{title}</Title>
                <Subtitle>{subtitle}</Subtitle>
              </div>
              <ConditionalWrapper
                condition={buttonDisabled}
                wrapper={children => (
                  <Tooltip title={buttonDisabledTooltip}>{children}</Tooltip>
                )}
              >
                <ButtonUI
                  size="lg"
                  onClick={onButtonClick}
                  disabled={buttonDisabled}
                >
                  {buttonLabel}
                </ButtonUI>
              </ConditionalWrapper>
            </HeaderWrapperUI>
            {shouldShowSearch && (
              <SearchBar
                dataCy={dataCy}
                ariaLabel={searchAriaLabel}
                isLoading={false}
                onSearch={onSearch}
                debounceDelay={0}
              />
            )}
          </>
        )
      }}
    ></PageHeaderUI>
  )
}
