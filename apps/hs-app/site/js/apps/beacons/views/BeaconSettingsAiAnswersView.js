/* global App appData */
import { CONTEXT_DEFAULT } from '../constants/suggestions'
import { sortEmptyQuestionLast } from '../react/components/SuggestedQuestions/SuggestedQuestions.utils'
import { sortEmptyItemsLast } from '../react/components/Suggestions/CustomSuggestions.utils'
import {
  CONTEXT_URL,
  INITIAL_ARRAY_SIZE,
} from '../react/components/SuggestionsByUrlBox/SuggestionsByUrlBox.constants'
import template from '../templates/beaconSettings.hbs'
import AddContextModalView from '../views/AddContextModalView'
import BrigadeFormView from './BrigadeFormView'
import SettingsAiAnswersFormWrapper from '@beacons/react/components/SettingsAiAnswersForm/SettingsAiAnswersFormWrapper'
import { ReactMounter } from '@helpscout/brigade'
import Backbone from 'hs-backbone'
import HS from 'hs-core'

const BeaconSettingsAiAnswersView = BrigadeFormView.extend({
  template,
  initialize({
    beaconPreview,
    navigate,
    viewModel = new Backbone.Model({
      cachedUrlContexts: {},
      isSiteSelected: false,
      topArticles: [],
    }),
    mailboxes,
  } = {}) {
    BrigadeFormView.prototype.initialize.apply(this, arguments)
    Object.assign(this, { beaconPreview, navigate, viewModel })
    this.setViewModel()

    this.viewModel.set('isDataLoading', false)

    if (this.model) {
      this.listenTo(this.model, 'change', this.setHasChanges)
    }

    this.mailboxes = this.transformMailboxes(mailboxes)

    if (!this.model.get('messagingMailboxId') && this.mailboxes.length > 0) {
      this.model.set('messagingMailboxId', this.mailboxes[0].value)
    }

    // To prevent leaving page when unsaved changes (until confirmed)
    // The AI improvements (isAiAnswersTidbitsEnabled) redesign handles this in react
    if (!window.hsGlobal.features.isAiAnswersTidbitsEnabled) {
      window.onbeforeunload = this.confirmExit.bind(this)
      this.setupBackboneHistoryInterception()
    }

    this.viewType = 'answers'
  },

  components() {
    const { memberPermissions } = hsGlobal
    const { accountOwnerEmail } = appData
    const accountOwnerFullName = window.appData
      ? window.appData.accountOwnerFullName || ''
      : ''

    return {
      '.js-form': {
        component: SettingsAiAnswersFormWrapper,
        initialState: {
          beacon: this.model,
          errors: this.errors,
          meta: this.meta,
          viewModel: this.viewModel,
          canManageDocsSettings: memberPermissions.manageDocsSettings,
          canManageAccount: memberPermissions.manageAccount,
          accountOwnerEmail,
          accountOwnerFullName,
          beaconPreview: this.beaconPreview,
          mailboxes: this.mailboxes,
        },
        externalActions: {
          handleAddOrEditUrlContext: this.handleAddOrEditUrlContext.bind(this),
          editTargetUrl: this.editTargetUrl.bind(this),
          handleSelectUrlContext: this.handleSelectUrlContext.bind(this),
          handleAiAnswersEnabledChange:
            this.handleAiAnswersEnabledChange.bind(this),
          handleAiAnswersSuggestedQuestionsChange:
            this.handleAiAnswersSuggestedQuestionsChange.bind(this),
          handleVoiceAndToneChange: this.handleVoiceAndToneChange.bind(this),
          handleExternalSourceAdded: this.handleExternalSourceAdded.bind(this),
          handleExternalSourceRemoved:
            this.handleExternalSourceRemoved.bind(this),
          onCancel: this.handleCancel.bind(this),
          onChange: this.handleChange.bind(this),
          onSave: this.handleSave.bind(this),
        },
      },
    }
  },

  setHasChanges() {
    this.hasChanges = true
  },

  confirmExit(e) {
    if (this.hasChanges) {
      const message =
        'You have unsaved changes. Are you sure you want to leave?'
      e.returnValue = message
      return message
    }
  },

  onBeforeDestroy() {
    window.onbeforeunload = null

    // Restore original Backbone.history function
    if (this._originalLoadUrl) {
      Backbone.history.loadUrl = this._originalLoadUrl
    }
  },

  setupBackboneHistoryInterception: function () {
    // Keep reference to the original function
    const originalLoadUrl = Backbone.history.loadUrl
    const self = this

    // Store the current fragment before navigation
    let currentFragment = Backbone.history.getFragment()

    // Listen for route changes to store the current fragment
    this.listenTo(Backbone.history, 'route', function () {
      currentFragment = Backbone.history.getFragment()
    })

    // Override the loadUrl method
    Backbone.history.loadUrl = function (fragmentOverride) {
      if (self.hasChanges) {
        if (
          !confirm('You have unsaved changes. Are you sure you want to leave?')
        ) {
          Backbone.history.navigate(currentFragment, {
            replace: true, // Use replace to avoid adding to browser history
            trigger: false, // Don't trigger route handlers
          })
          return false
        }
        // User confirmed, clear changes flag
        self.hasChanges = false
      }
      // Proceed with original navigation
      return originalLoadUrl.apply(this, arguments)
    }

    // Store original function to restore later
    this._originalLoadUrl = originalLoadUrl
  },

  setViewModel() {
    const docsSiteId = this.model.get('docsSiteId')
    this.viewModel.set('isSiteSelected', !!(docsSiteId && docsSiteId.length))
    this.viewModel.set(
      'suggestedQuestionsCurrentUrlContext',
      this.getDefaultUrlContext(CONTEXT_URL.SUGGESTED_QUESTIONS)
    )
  },

  handleAiAnswersEnabledChange() {
    this.handleChange('aiAnswersEnabled')(!this.model.get('aiAnswersEnabled'))
    this.beaconPreview.setDemoAiAnswers(this.model.get('aiAnswersEnabled'))
    const CURRENT_URL_CONTEXT = this.getCurrentUrlContextName(
      CONTEXT_URL.SUGGESTED_QUESTIONS
    )
    this.beaconPreview.setSuggestedQuestions(
      this.viewModel.get(CURRENT_URL_CONTEXT).suggestions
    )
  },

  handleAiAnswersSuggestedQuestionsChange(suggestions) {
    const CURRENT_URL_CONTEXT = this.getCurrentUrlContextName(
      CONTEXT_URL.SUGGESTED_QUESTIONS
    )

    // put the empty questions last
    const nextSuggestions = [...suggestions].sort(sortEmptyQuestionLast)

    const currentUrlContext = this.viewModel.get(CURRENT_URL_CONTEXT)
    const contextIndex = this.getUrlContextIndex(
      currentUrlContext,
      CONTEXT_URL.SUGGESTED_QUESTIONS
    )
    const prevSuggestionedQuestions = this.model
      .get(CONTEXT_URL.SUGGESTED_QUESTIONS)
      .slice(0)
    const nextSuggestionedQuestions = this.model
      .get(CONTEXT_URL.SUGGESTED_QUESTIONS)
      .slice(0)

    nextSuggestionedQuestions[contextIndex] = {
      ...nextSuggestionedQuestions[contextIndex],
      suggestions: nextSuggestions,
    }

    this.model.set(CONTEXT_URL.SUGGESTED_QUESTIONS, nextSuggestionedQuestions)
    this.viewModel.set(CURRENT_URL_CONTEXT, {
      ...prevSuggestionedQuestions[contextIndex],
      suggestions: nextSuggestions,
    })

    this.handleChange('suggestedQuestions')(nextSuggestionedQuestions)
    this.beaconPreview.setSuggestedQuestions(
      nextSuggestions.filter(item => item && item.question?.trim())
    )
  },

  handleVoiceAndToneChange(text) {
    this.handleChange('voiceAndTonePromptText')(text)
    this.beaconPreview.setDemoAiAnswersValues(this.model.get('id'), {
      voiceAndTone: text,
      docsSiteId: this.model.get('docsSiteId'),
      externalSourceIds: this.model.get('externalSourceIds'),
      snippetIds: this.model.get('snippetIds'),
    })
  },

  handleExternalSourceAdded(idAdded, type) {
    const key = type === 'snippet' ? 'snippetIds' : 'externalSourceIds'
    const ids = this.model.get(key) || []
    const newIds = [...ids, idAdded]
    this.handleChange(key)(newIds)
    this.beaconPreview.setDemoAiAnswersValues(this.model.get('id'), {
      voiceAndTone: this.model.get('voiceAndTonePromptText'),
      docsSiteId: this.model.get('docsSiteId'),
      externalSourceIds: this.model.get('externalSourceIds'),
      snippetIds: this.model.get('snippetIds'),
    })
  },

  handleExternalSourceRemoved(idRemoved, type) {
    const key = type === 'snippet' ? 'snippetIds' : 'externalSourceIds'
    const ids = this.model.get(key) || []
    const newIds = ids.filter(item => item !== idRemoved)
    this.handleChange(key)(newIds)
    this.beaconPreview.setDemoAiAnswersValues(this.model.get('id'), {
      voiceAndTone: this.model.get('voiceAndTonePromptText'),
      docsSiteId: this.model.get('docsSiteId'),
      externalSourceIds: this.model.get('externalSourceIds'),
      snippetIds: this.model.get('snippetIds'),
    })
  },

  handleCancel() {
    window.location.reload()
  },

  handleSaveSuccess() {
    HS.Utils.Main.success('Beacon updated')
    this.hasChanges = false
  },

  onShow() {
    this.beaconPreview.initDocs(
      this.model.get('docsSearchEnabled'),
      this.model.get('aiAnswersEnabled')
    )
  },

  getTargetUrls(target = CONTEXT_URL.SUGGESTED_QUESTIONS) {
    return this.model
      .get(target)
      .map(context => {
        if (typeof context.targetUrl === 'string') {
          return context.targetUrl.toLowerCase()
        }
        return ''
      })
      .filter(targetUrl => !!targetUrl)
  },

  getDefaultUrlContext(target = CONTEXT_URL.SUGGESTED_QUESTIONS) {
    const results = this.model.get(target).filter((urlContext = {}) => {
      return urlContext.type === CONTEXT_DEFAULT
    })

    if (results && results.length) {
      const result = results[0]

      if (result.suggestions) {
        result.suggestions.sort(sortEmptyItemsLast)
      }
      return {
        ...result,
      }
    }
    return {
      type: CONTEXT_DEFAULT,
      suggestions: Array.apply(null, Array(INITIAL_ARRAY_SIZE[target])).map(
        () => ({})
      ),
    }
  },

  getUrlContextByTargetUrl(
    targetUrl,
    target = CONTEXT_URL.SUGGESTED_QUESTIONS
  ) {
    return this.model
      .get(target)
      .find(context => context.targetUrl === targetUrl)
  },

  getUrlContextIndex(context, target = CONTEXT_URL.SUGGESTED_QUESTIONS) {
    return this.model
      .get(target)
      .findIndex(_context => _context.targetUrl === context?.targetUrl)
  },

  getCurrentUrlContextName(target) {
    return target === CONTEXT_URL.SUGGESTED_QUESTIONS
      ? `${target}CurrentUrlContext`
      : 'currentUrlContext'
  },

  editTargetUrl(
    currentContext,
    newTargetUrl,
    target = CONTEXT_URL.SUGGESTED_QUESTIONS
  ) {
    const newContext = { ...currentContext, targetUrl: newTargetUrl }

    this.onAddOrEditUrlContext(newContext, currentContext, target)
  },

  handleAddOrEditUrlContext(context, target = CONTEXT_URL.SUGGESTED_QUESTIONS) {
    const initialArraySize = INITIAL_ARRAY_SIZE[target]
    const isEditing = !!context
    const modalModel = isEditing
      ? new Backbone.Model(context)
      : new Backbone.Model({
          suggestions: Array.apply(null, Array(initialArraySize)).map(
            () => ({})
          ),
          targetUrl: '',
          type: 'url',
        })
    const modalView = App.modalRegion.wrapAndShow(
      new AddContextModalView({
        isEditing,
        model: modalModel,
        prevTargetUrl: isEditing ? context.targetUrl : '',
        targetUrls: this.getTargetUrls(target),
      }),
      {
        header: isEditing ? 'Edit URL' : 'Add a URL',
      }
    )

    modalModel.on('change', () => {
      modalView.close()
      const nextContext = modalModel.toJSON()
      return isEditing
        ? this.onAddOrEditUrlContext(nextContext, context, target)
        : this.onAddOrEditUrlContext(nextContext, undefined, target)
    })
  },

  onAddOrEditUrlContext(
    nextContext,
    prevContext,
    target = CONTEXT_URL.SUGGESTED_QUESTIONS
  ) {
    const suggestions = this.model.get(target)
    const CURRENT_URL_CONTEXT = this.getCurrentUrlContextName(target)

    if (nextContext.targetUrl === undefined) {
      // if undefined, delete it
      const contextIndex = this.getUrlContextIndex(prevContext, target)
      const newSuggestions = [...suggestions]
      newSuggestions.splice(contextIndex, 1)
      this.handleChange(target)(newSuggestions)
      nextContext = this.getDefaultUrlContext(target)
    } else if (prevContext) {
      // else if there is already a context  with this target url, replace it
      const contextIndex = this.getUrlContextIndex(prevContext, target)
      const newSuggestions = [...suggestions]
      newSuggestions[contextIndex] = nextContext
      this.handleChange(target)(newSuggestions)
    } else {
      // else just add it
      this.handleChange(target)([nextContext, ...suggestions])
    }
    this.viewModel.set(CURRENT_URL_CONTEXT, nextContext)
    if (target === CONTEXT_URL.SUGGESTED_QUESTIONS) {
      this.beaconPreview.setSuggestedQuestions(nextContext.suggestions)
    } else {
      this.setPreviewSuggestions()
    }
  },

  handleSelectUrlContext(targetUrl, target = CONTEXT_URL.SUGGESTED_QUESTIONS) {
    const CURRENT_URL_CONTEXT = this.getCurrentUrlContextName(target)
    let currentSuggestions

    if (!targetUrl) {
      currentSuggestions = this.getDefaultUrlContext(target)
    } else {
      currentSuggestions = this.getUrlContextByTargetUrl(targetUrl, target)
    }

    //maybe set default with empty values here if there is nothing
    this.viewModel.set(CURRENT_URL_CONTEXT, currentSuggestions)
    if (target === CONTEXT_URL.SUGGESTED_QUESTIONS) {
      this.beaconPreview.setSuggestedQuestions(currentSuggestions.suggestions)
    } else {
      this.setPreviewSuggestions()
    }
  },

  hasDefaultContextSuggestions(docsSuggestions) {
    const defaultContext = docsSuggestions.find(context => {
      return context.type === CONTEXT_DEFAULT
    })

    if (defaultContext) {
      // Filter out empty suggestions
      const suggestions = defaultContext.suggestions.filter(
        suggestion => suggestion.type
      )

      return suggestions.length > 0
    }

    return false
  },

  transformMailboxes(mailboxes) {
    let result = []

    if (mailboxes) {
      result = mailboxes.map(mailbox => {
        return {
          label: mailbox.name,
          value: `${mailbox.id}`, // Cast number to string
        }
      })
    }

    return result
  },
})

export default ReactMounter(BeaconSettingsAiAnswersView)
