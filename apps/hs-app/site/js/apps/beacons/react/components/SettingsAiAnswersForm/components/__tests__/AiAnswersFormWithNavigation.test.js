import useAiAnswersFormChanges from '../../hooks/useAiAnswersFormChanges'
import AiAnswersFormWithNavigation from '../AiAnswersFormWithNavigation'
import { render } from '@testing-library/react'
import Backbone from 'hs-backbone'
import React from 'react'
import { MemoryRouter } from 'react-router-dom'

const mockBlock = jest.fn()
const mockUnblock = jest.fn()
const mockConfirm = jest.fn()
const originalConfirm = window.confirm

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useHistory: () => ({
    block: mockBlock.mockReturnValue(mockUnblock),
  }),
}))

jest.mock('../../routes/SettingsAiAnswersRoutes', () => () => (
  <div>Mock Routes</div>
))
jest.mock('../../hooks/useAiAnswersFormChanges')
jest.mock('hs-backbone', () => {
  const RequestResponse = jest.fn()
  const mockLoadUrl = jest.fn()
  return {
    history: {
      loadUrl: mockLoadUrl,
      navigate: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      getFragment: jest
        .fn()
        .mockReturnValue('/settings/beacons/1/answers/configure'),
    },
    Wreqr: {
      RequestResponse,
    },
  }
})

const renderWithRouter = ({ hasChanges = false, confirm = false } = {}) => {
  useAiAnswersFormChanges.mockReturnValue(hasChanges)
  mockConfirm.mockReturnValue(confirm)

  return render(
    <MemoryRouter>
      <AiAnswersFormWithNavigation />
    </MemoryRouter>
  )
}

describe('<AiAnswersFormWithNavigation />', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    window.confirm = mockConfirm
  })

  afterEach(() => {
    window.confirm = originalConfirm
  })

  describe('React router history', () => {
    it('should cleanup navigation blocking when unmounting', () => {
      const { unmount } = renderWithRouter({ hasChanges: true })

      expect(mockUnblock).not.toHaveBeenCalled()
      unmount()
      expect(mockUnblock).toHaveBeenCalledTimes(1)
    })

    it('should not block navigation when there are no changes', () => {
      renderWithRouter({ hasChanges: false, confirm: true })

      expect(mockBlock).not.toHaveBeenCalled()
    })

    it('should block navigation and show the prompt when a user has unsaved changes', () => {
      renderWithRouter({ hasChanges: true, confirm: true })

      const blockCallback = mockBlock.mock.calls[0][0]
      const result = blockCallback({ pathname: '/some-other-route' }, 'PUSH')
      expect(result).toBe(true)
      expect(mockConfirm).toHaveBeenCalledWith(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })

    it('should prevent navigation when a user cancels the prompt', () => {
      renderWithRouter({ hasChanges: true, confirm: false })

      const blockCallback = mockBlock.mock.calls[0][0]
      const result = blockCallback({ pathname: '/some-other-route' }, 'POP')
      expect(result).toBe(false)
      expect(mockConfirm).toHaveBeenCalledWith(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })

    it('should allow navigation when a user confirms the prompt', () => {
      renderWithRouter({ hasChanges: true, confirm: true })

      const blockCallback = mockBlock.mock.calls[0][0]
      const result = blockCallback({ pathname: '/some-other-route' }, 'REPLACE')
      expect(result).toBe(true)
      expect(mockConfirm).toHaveBeenCalledWith(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })
  })

  describe('Backbone history', () => {
    it('should not call navigate to restore the current route when a user confirms the prompt', () => {
      renderWithRouter({ hasChanges: true, confirm: true })

      Backbone.history.loadUrl('/settings/beacons/1/answers/configure')

      expect(Backbone.history.navigate).not.toHaveBeenCalled()
      expect(mockConfirm).toHaveBeenCalledWith(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })

    it('should prevent navigation and restore current route when a user cancels the prompt', () => {
      renderWithRouter({ hasChanges: true, confirm: false })

      Backbone.history.loadUrl('/settings/beacons/1/answers/configure')
      expect(Backbone.history.navigate).toHaveBeenCalledWith(
        '/settings/beacons/1/answers/configure',
        {
          replace: true,
          trigger: false,
        }
      )
      expect(mockConfirm).toHaveBeenCalledWith(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })
  })

  describe('beforeunload event handler', () => {
    const mockAddEventListener = jest.fn()
    const mockRemoveEventListener = jest.fn()
    const originalAddEventListener = window.addEventListener
    const originalRemoveEventListener = window.removeEventListener

    beforeEach(() => {
      window.addEventListener = mockAddEventListener
      window.removeEventListener = mockRemoveEventListener
    })

    afterEach(() => {
      window.addEventListener = originalAddEventListener
      window.removeEventListener = originalRemoveEventListener
    })

    it('should add a beforeunload event listener when there are unsaved changes', () => {
      renderWithRouter({ hasChanges: true })

      expect(mockAddEventListener).toHaveBeenCalledWith(
        'beforeunload',
        expect.any(Function)
      )
    })

    it('should not add a beforeunload event listener when there are no changes', () => {
      renderWithRouter({ hasChanges: false })

      const beforeUnloadCalls = mockAddEventListener.mock.calls.filter(
        call => call[0] === 'beforeunload'
      )
      expect(beforeUnloadCalls).toHaveLength(0)
    })

    it('should remove the beforeunload event listener on unmount', () => {
      const { unmount } = renderWithRouter({ hasChanges: true })

      unmount()

      const beforeUnloadCalls = mockRemoveEventListener.mock.calls.filter(
        call => call[0] === 'beforeunload'
      )
      expect(beforeUnloadCalls).toHaveLength(1)
      expect(beforeUnloadCalls[0][0]).toBe('beforeunload')
      expect(typeof beforeUnloadCalls[0][1]).toBe('function')
    })

    it('should show a confirmation dialog when the beforeunload event is triggered', () => {
      renderWithRouter({ hasChanges: true })

      const beforeUnloadCalls = mockAddEventListener.mock.calls.filter(
        call => call[0] === 'beforeunload'
      )
      const beforeUnloadHandler = beforeUnloadCalls[0][1]
      const event = { preventDefault: jest.fn() }
      const result = beforeUnloadHandler(event)

      expect(result).toBe(
        'You have unsaved changes. Are you sure you want to leave?'
      )
    })
  })
})
