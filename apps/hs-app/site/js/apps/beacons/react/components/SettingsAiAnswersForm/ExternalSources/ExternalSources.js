import { useFetchExternalSources } from './ExternalSources.hooks'
import { ExternalSourcesHeader } from './components/ExternalSourcesHeader'
import { Websites } from './components/Websites'
import { useAiAnswersFormContext } from '@beacons/react/components/SettingsAiAnswersForm/AiAnswersFormContext'
import { Alert } from 'hsds/components/alert'
import Page from 'hsds/components/page'
import { Spinner } from 'hsds/components/spinner'
import React, { useMemo } from 'react'
import styled from 'styled-components'

const PageCardUI = styled(Page.Card)`
  z-index: 1;
  position: relative;
`

const SpinnerContainerUI = styled.div`
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  height: 200px;
`

export const ExternalSources = () => {
  const { externalSourceIds } = useAiAnswersFormContext()
  const { data, isLoading, isError } = useFetchExternalSources()

  // filter out for only Beacon related sources
  const currentData = useMemo(() => {
    const sources =
      data?.sources?.filter(source => externalSourceIds?.includes(source.id)) ||
      []

    sources.sort((a, b) => a.id - b.id)

    return {
      ...data,
      sources,
    }
  }, [data, externalSourceIds])

  function getContent() {
    if (isLoading) {
      return (
        <SpinnerContainerUI>
          <Spinner size={30} />
        </SpinnerContainerUI>
      )
    }

    if (isError) {
      return (
        <Alert status="error">
          There was an error fetching Additional Sources, please try again.
        </Alert>
      )
    }

    return (
      <>
        <Websites data={currentData} />
      </>
    )
  }

  return (
    <PageCardUI>
      <ExternalSourcesHeader />
      {getContent()}
    </PageCardUI>
  )
}
