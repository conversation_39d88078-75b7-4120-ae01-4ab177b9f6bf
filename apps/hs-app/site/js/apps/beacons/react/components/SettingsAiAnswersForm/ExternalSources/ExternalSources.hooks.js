import { isTestEnv } from '@common/utils/env'
import { trpc } from 'shared/utils/trpc'

export function useFetchExternalSources() {
  const { data, isLoading, isError, refetch } =
    trpc.externalSources.getExternalSources.useQuery(undefined, {
      networkMode: 'always',
      staleTime: Infinity,
      cacheTime: 0,
      retry: isTestEnv() ? 0 : 1,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      useErrorBoundary: false,
      refetchInterval: data => {
        if (
          data?.sources?.some(source => source.syncStatus === 'in_progress')
        ) {
          return 30000
        }
        return false
      },
    })

  return { data, isLoading, isError, refetch }
}

export function useAddExternalSource() {
  const trpcUtils = trpc.useUtils()
  const { mutate: addExternalSource, isLoading: isAdding } =
    trpc.externalSources.createExternalSource.useMutation({
      onSuccess: (response, input) => {
        const oldData = trpcUtils.externalSources.getExternalSources.getData()

        // do not add source placeholder if it already exists
        if (oldData.sources.some(source => source.id === response.id)) {
          return
        }
        const newData = {
          ...oldData,
          sources: [
            ...(oldData?.sources || []),
            {
              id: response.id,
              url: input.url,
              syncStatus: 'in_progress',
              syncedAt: new Date().toISOString(),
              name: 'Unknown',
            },
          ],
        }
        // Optimistic update
        // first use response to set item immediately
        trpcUtils.externalSources.getExternalSources.setData(undefined, newData)

        // then refetch all to get correct data
        void trpcUtils.externalSources.getExternalSources.invalidate()
      },
      onError: error => {
        console.error(`Unable to add external source: ${error.message}`)
        switch (error.message) {
          case 'PAGE_LIMIT_EXCEEDED':
            HS.Utils.Main.error(
              "Website not added. You've reached the page limit."
            )
            break
          case 'INVALID_SOURCE':
            HS.Utils.Main.error(
              "Oops! That URL didn't return any results. Check and try again."
            )
            break
          default:
            HS.Utils.Main.error('Unable to add source. Please try again.')
        }
      },
    })

  return { addExternalSource, isAdding }
}

export function useEditExternalSource() {
  const trpcUtils = trpc.useUtils()
  const { mutate: editExternalSource, isLoading: isEditing } =
    trpc.externalSources.updateExternalSource.useMutation({
      onSuccess: () => {
        void trpcUtils.externalSources.getExternalSources.invalidate()
      },
      onError: error => {
        console.error(`Unable to update external source: ${error.message}`)
        switch (error.message) {
          case 'PAGE_LIMIT_EXCEEDED':
            HS.Utils.Main.error(
              "Website not updated. You've reached the page limit."
            )
            break
          case 'INVALID_SOURCE':
            HS.Utils.Main.error(
              "Oops! That URL didn't return any results. Check and try again."
            )
            break
          default:
            HS.Utils.Main.error('Unable to update source. Please try again.')
        }
      },
    })

  return { editExternalSource, isEditing }
}


export function useResyncExternalSource() {
  const trpcUtils = trpc.useUtils()
  const { mutate: resyncExternalSource, isLoading: isResyncing } =
    trpc.externalSources.resyncExternalSource.useMutation({
      onSuccess: (response, input) => {
        trpcUtils.externalSources.getExternalSources.setData(
          undefined,
          old => ({
            ...(old || {}),
            sources:
              old?.sources?.map(item =>
                item.id === input
                  ? {
                      ...item,
                      syncStatus: 'in_progress',
                    }
                  : item
              ) || [],
          })
        )
      },
      onError: error => {
        console.error(`Unable to resync external source: ${error.message}`)
        HS.Utils.Main.error('Unable to resync source. Please try again.')
      },
    })

  return { resyncExternalSource, isResyncing }
}

export function useGetWebsiteMap(url) {
  const { data, isLoading, isError } =
    trpc.externalSources.getWebsiteMap.useQuery(
      { url },
      {
        networkMode: 'always',
        staleTime: Infinity,
        cacheTime: 0,
        retry: isTestEnv() ? 0 : 1,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        useErrorBoundary: false,
      }
    )
  return { data, isLoading, isError }
}
