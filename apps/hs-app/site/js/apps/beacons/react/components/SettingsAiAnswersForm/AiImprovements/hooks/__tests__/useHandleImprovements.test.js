import useHandleImprovements from '../useHandleImprovements'
import { renderHook } from '@testing-library/react-hooks'

const mockData = {
  sources: [
    {
      id: '1',
      name: 'First Improvement',
      text: 'This is the first improvement',
      createdAt: '2024-01-01T00:00:00Z',
      syncedAt: null,
      updatedBy: null,
      type: 'snippet',
    },
    {
      id: '2',
      name: 'Second Improvement',
      text: 'This is the second improvement',
      createdAt: '2024-01-02T00:00:00Z',
      syncedAt: '2024-01-03T00:00:00Z',
      updatedBy: 'user1',
      type: 'snippet',
    },
    {
      id: '3',
      name: 'Third Improvement',
      text: 'This is the third improvement with a keyword that should be found',
      createdAt: '2024-01-03T00:00:00Z',
      syncedAt: null,
      updatedBy: null,
      type: 'snippet',
    },
    {
      id: '4',
      name: 'Not a snippet',
      type: 'public_site',
    },
  ],
}

describe('useHandleImprovements', () => {
  it('should filter improvements by type of snippet and snippetIds', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: mockData,
        currentPage: 1,
        snippetIds: ['1', '2'],
      })
    )

    expect(result.current.currentImprovements).toHaveLength(2)
    expect(result.current.totalItems).toBe(2)
    expect(result.current.currentImprovements.map(i => i.id)).toEqual([
      '2',
      '1',
    ])
  })

  it('should return empty array when no matching snippetIds are provided', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: mockData,
        currentPage: 1,
        snippetIds: ['999'],
      })
    )

    expect(result.current.currentImprovements).toHaveLength(0)
    expect(result.current.totalItems).toBe(0)
  })

  it('should sort improvements by syncedAt if updatedBy exists, otherwise by createdAt', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: mockData,
        currentPage: 1,
        snippetIds: ['1', '2', '3'],
      })
    )

    const improvements = result.current.currentImprovements
    expect(improvements[0].id).toBe('2') // Should be first b/c it has syncedAt that is the most recent
    expect(improvements[1].id).toBe('3') // Second b/c no syncedAt, but has createdAt 2nd most recent
    expect(improvements[2].id).toBe('1') // Oldest
  })

  it('should filter improvements by search value for name and be case insensitive', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: mockData,
        currentPage: 1,
        searchValue: 'Second',
        snippetIds: ['1', '2', '3'],
      })
    )

    expect(result.current.currentImprovements).toHaveLength(1)
    expect(result.current.totalItems).toBe(1)
    expect(result.current.currentImprovements[0].id).toBe('2')
  })

  it('should filter improvements by search value for text and be case insensitive', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: mockData,
        currentPage: 1,
        searchValue: 'Keyword',
        snippetIds: ['1', '2', '3'],
      })
    )

    expect(result.current.currentImprovements).toHaveLength(1)
    expect(result.current.totalItems).toBe(1)
    expect(result.current.currentImprovements[0].id).toBe('3')
  })

  it('should return empty array when no data is provided', () => {
    const { result } = renderHook(() =>
      useHandleImprovements({
        data: null,
        currentPage: 1,
        snippetIds: ['1', '2', '3'],
      })
    )

    expect(result.current.currentImprovements).toEqual([])
    expect(result.current.totalItems).toBe(0)
  })
})
