import { useAiAnswersFormContext } from '../../AiAnswersFormContext'
import useAiAnswersFormChanges from '../useAiAnswersFormChanges'
import { renderHook } from '@testing-library/react-hooks'
import { useLocation } from 'react-router-dom'

jest.mock('react-router-dom', () => ({
  useLocation: jest.fn(),
}))

jest.mock('../../AiAnswersFormContext', () => ({
  useAiAnswersFormContext: jest.fn(),
}))

describe('useAiAnswersFormChanges', () => {
  const mockContextValues = {
    aiAnswersEnabled: false,
    docsSearchEnabled: false,
    voiceAndTonePromptText: '',
    externalSourceIds: [],
    isSiteSelected: false,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    useLocation.mockReturnValue({ pathname: '/configure' })
    useAiAnswersFormContext.mockReturnValue(mockContextValues)
  })

  it('should initially default to false when no changes are made', () => {
    const { result } = renderHook(() => useAiAnswersFormChanges())
    expect(result.current).toBe(false)
  })

  it('should detect changes in the configure tab values', () => {
    const { result, rerender } = renderHook(() => useAiAnswersFormChanges())

    useAiAnswersFormContext.mockReturnValue({
      ...mockContextValues,
      aiAnswersEnabled: true,
    })

    expect(result.current).toBe(false)
    rerender()
    expect(result.current).toBe(true)
  })

  it('should maintain separate change states for each tab', () => {
    const { result, rerender } = renderHook(() => useAiAnswersFormChanges())

    useAiAnswersFormContext.mockReturnValue({
      ...mockContextValues,
      aiAnswersEnabled: true,
    })
    rerender()

    useLocation.mockReturnValue({ pathname: '/improvements' })
    rerender()
    expect(result.current).toBe(false)

    useLocation.mockReturnValue({ pathname: '/configure' })
    rerender()
    expect(result.current).toBe(true)
  })

  it('should not detect changes when values are equal', () => {
    const { result, rerender } = renderHook(() => useAiAnswersFormChanges())

    useAiAnswersFormContext.mockReturnValue({
      ...mockContextValues,
      aiAnswersEnabled: false,
    })

    expect(result.current).toBe(false)
    rerender()
    expect(result.current).toBe(false)
  })
})
