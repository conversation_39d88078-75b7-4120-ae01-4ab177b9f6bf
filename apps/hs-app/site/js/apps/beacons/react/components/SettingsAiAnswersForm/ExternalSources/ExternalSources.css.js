import Button from 'hsds/components/button'
import ButtonIcon from 'hsds/components/button-icon'
import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const SourceItemUI = styled.li`
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin: 0;

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  & + &:not(:empty) {
    border-top: 1px solid ${getColor('charcoal.400')};
  }

  &.failed {
    background-color: ${getColor('magenta.100')};
  }
`

export const WebsiteInfoUI = styled.div`
  color: ${getColor('charcoal.800')};
  flex-grow: 1;
  margin-right: 4px;

  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  &.in_progress {
    color: ${getColor('charcoal.600')};
  }

  &.failed {
    color: ${getColor('red.600')};
  }
`

export const WebsiteNameUI = styled.div`
  font-weight: 500;
  line-height: normal;
  font-size: 13px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`
export const WebsiteUrlUI = styled.div`
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`

export const SyncStatusUI = styled.div`
  margin-left: auto;
  width: 24px;
  height: 24px;

  flex-shrink: 0;

  display: flex;
  align-items: center;
  justify-content: center;

  color: white;
  background-color: var(--sync-status-color);
  border-radius: 100px;

  &.in_progress {
    animation: spin 3s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`

export const SourcesListUI = styled.ul`
  list-style: none;
  padding: 0;
  border: 1px solid ${getColor('charcoal.400')};
  border-radius: 4px;

  margin: 0;
`

export const SourcesContainerUI = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;

  & + & {
    margin-top: 40px;
  }
`

export const SourcesHeaderUI = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;

  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  color: ${getColor('charcoal.700')};
`

export const SourcesHeaderTitleUI = styled.div`
  font-size: 16px;
  font-weight: 500;
  line-height: 19.2px;
  color: ${getColor('charcoal.1200')};
`

export const AddSourceButtonUI = styled(Button)`
  margin-top: 15px;
  width: max-content;
`

export const AddWebsiteButtonUI = styled(AddSourceButtonUI)`
  margin-bottom: 30px;
`

export const AddWithExistingButtonUI = styled(Button)`
  width: max-content;
  margin: 9px 10px;
`

export const ExternalSourcesSectionUI = styled(Page)`
  --hsds-page-margin-top: 0;
`

export const AddItemUI = styled(SourceItemUI)`
  padding: 0;
  width: 100%;
  display: flex;
`

export const FormUI = styled.div`
  display: flex;
  gap: 4px;
  padding: 4px;

  flex-grow: 1;

  .c-Input__suffix {
    display: flex;
    gap: 4px;
  }
`


export const UsageContainerUI = styled.div`
  display: flex;
  flex-direction: column;

  color: ${getColor('charcoal.800')};
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
`

export const UsageProgressBarContainerUI = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`

export const UsageProgressBarUI = styled.div`
  flex-grow: 1;
  height: 12px;
  background-color: ${getColor('charcoal.200')};
  border-radius: 100px;
`

export const UsageProgressBarFillUI = styled.div`
  width: var(--usage-progress, 0%);
  min-width: 5%;
  height: 12px;
  border-radius: 100px;

  background: linear-gradient(
    90deg,
    var(--hsds-color-green-300) 0%,
    var(--hsds-color-green-500) 100%
  );
`

export const UsageTextUI = styled.div`
  font-size: 13px;
  font-weight: 400;
  line-height: 22px;
  flex-shrink: 0;
  color: ${getColor('charcoal.600')};
  margin-left: auto;
`
