import { useAiAnswersFormContext } from '../../AiAnswersFormContext'
import { useFetchExternalSources } from '../../ExternalSources/ExternalSources.hooks'
import useHandleImprovements from '../hooks/useHandleImprovements'
import PropTypes from 'prop-types'
import React, { createContext, useContext, useState, useCallback } from 'react'

export const IMPROVEMENTS_PER_PAGE = 10

export const AiImprovementsContext = createContext(null)

export const useAiImprovementsContext = () => {
  const context = useContext(AiImprovementsContext)

  if (!context) {
    throw new Error(
      'useAiImprovementsContext must be used within an AiImprovementsProvider'
    )
  }

  return context
}

const AiImprovementsProvider = ({ children }) => {
  const [editId, setEditId] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState('')

  const {
    data,
    isLoading,
    isError,
    refetch: refetchExternalSources,
  } = useFetchExternalSources()
  const { snippetIds } = useAiAnswersFormContext()

  const { currentImprovements, totalItems } = useHandleImprovements({
    data,
    snippetIds,
    currentPage,
    searchValue,
    improvementsPerPage: IMPROVEMENTS_PER_PAGE,
  })

  const handlePageChange = useCallback(nextPage => {
    setCurrentPage(nextPage)
  }, [])

  const handleSearchChange = useCallback(
    val => {
      setSearchValue(val)
      if (currentPage !== 1) {
        setCurrentPage(1)
      }
    },
    [currentPage]
  )

  const handleRefetchSources = useCallback(async () => {
    await refetchExternalSources()
    if (currentPage !== 1) {
      setCurrentPage(1)
    }

    if (editId) {
      setEditId(null)
    }

    setSearchValue('')
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [refetchExternalSources, currentPage, editId])

  const handleEditIdChange = id => setEditId(id)

  return (
    <AiImprovementsContext.Provider
      value={{
        editId,
        isError,
        isLoading,
        totalItems,
        currentPage,
        searchValue,
        setSearchValue,
        handlePageChange,
        handleSearchChange,
        handleEditIdChange,
        currentImprovements,
        handleRefetchSources,
      }}
    >
      {children}
    </AiImprovementsContext.Provider>
  )
}

AiImprovementsProvider.propTypes = {
  children: PropTypes.node.isRequired,
}

export default AiImprovementsProvider
