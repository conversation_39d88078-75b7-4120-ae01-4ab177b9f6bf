import useAiAnswersFormChanges from '../hooks/useAiAnswersFormChanges'
import SettingsAiAnswersRoutes from '../routes/SettingsAiAnswersRoutes'
import AiAnswersTabbedNavigation from './AiAnswersTabbedNavigation'
import Backbone from 'hs-backbone'
import React, { useEffect } from 'react'
import { useHistory } from 'react-router-dom'

const NAVIGATION_ACTIONS = ['PUSH', 'REPLACE', 'POP']
const UNSAVED_CHANGES_MESSAGE =
  'You have unsaved changes. Are you sure you want to leave?'

const confirmNavigation = () => window.confirm(UNSAVED_CHANGES_MESSAGE)

const handleBeforeUnload = e => {
  e.preventDefault()
  e.returnValue = UNSAVED_CHANGES_MESSAGE
  return UNSAVED_CHANGES_MESSAGE
}

const AiAnswersFormWithNavigation = () => {
  const history = useHistory()
  const hasChanges = useAiAnswersFormChanges()

  /**
   * @description
   * This effect is used to handle the react router history between tabs
   * and backbone history outside of tabs (e.g. when clicking on a link in the sidebar)
   * This replaces the logic in the BeaconSettingsAiAnswersView.js to intercepet backbone history
   * and handle the unsaved changes confirmation
   */
  useEffect(() => {
    if (!hasChanges) return
    // Handle react router history between tabs
    const unblock = history.block((_location, action) => {
      if (NAVIGATION_ACTIONS.includes(action)) {
        return confirmNavigation()
      }
      return true
    })

    // Add window.onbeforeunload as a fallback for parity with what BeaconSettingsAiAnswersView.js does
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Handle backbone history outside of tabs (e.g. when clicking on a link in the sidebar)
    const originalLoadUrl = Backbone.history.loadUrl
    let currentFragment = Backbone.history.getFragment()

    const routeHandler = () => {
      currentFragment = Backbone.history.getFragment()
    }
    Backbone.history.on('route', routeHandler)

    Backbone.history.loadUrl = function (_fragment) {
      if (confirmNavigation()) {
        return originalLoadUrl.apply(this, arguments)
      }
      // Restore the previous URL without triggering route handlers
      Backbone.history.navigate(currentFragment, {
        replace: true,
        trigger: false,
      })
      return false
    }

    return () => {
      unblock()
      Backbone.history.off('route', routeHandler)
      Backbone.history.loadUrl = originalLoadUrl
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [hasChanges, history])

  return (
    <>
      <AiAnswersTabbedNavigation />
      <SettingsAiAnswersRoutes />
    </>
  )
}

export default AiAnswersFormWithNavigation
