import { AiAnswersFormContext } from '../../AiAnswersFormContext'
import AiImprovementsList from '../AiImprovementsList'
import { AiImprovementsContext } from '../context/AiImprovementsContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { useDeleteImprovement } from 'shared/components/ImprovementComposer/Edit/hooks/useDeleteImprovement'
import { createTrpcClient, TrpcProvider } from 'shared/utils/trpc'

jest.mock(
  'shared/components/ImprovementComposer/Edit/hooks/useDeleteImprovement',
  () => ({
    useDeleteImprovement: jest.fn(),
  })
)

const mockImprovements = [
  {
    id: 1,
    name: 'First Improvement',
    text: 'First Description',
    createdAt: '2024-03-20T10:00:00Z',
    syncStatus: 'completed',
    syncedAt: '2024-03-20T10:00:00Z',
    type: 'snippet',
  },
  {
    id: 2,
    name: 'Second Improvement',
    text: 'Second Description',
    createdAt: '2024-03-21T10:00:00Z',
    syncStatus: 'completed',
    syncedAt: '2024-03-21T10:00:00Z',
    type: 'snippet',
  },
]

const mockHandleExternalSourceRemoved = jest.fn()
const mockUseDeleteImprovement = useDeleteImprovement
const mockShowConfirmNoty = jest.fn()

const mockContextValue = {
  editId: null,
  handleEditIdChange: jest.fn(),
  currentImprovements: mockImprovements,
  handleRefetchSources: jest.fn(),
}

const mockAiAnswersFormContextValue = {
  beaconId: 'test-beacon-id',
  handleExternalSourceRemoved: mockHandleExternalSourceRemoved,
}

const renderWithContext = (overrides = {}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })
  const trpcClient = createTrpcClient()

  mockUseDeleteImprovement.mockImplementation(({ onSuccess }) => ({
    deleteImprovement: () => {
      mockShowConfirmNoty('Delete improvement', {
        onConfirm: () => onSuccess?.(),
      })
    },
  }))

  return render(
    <TrpcProvider queryClient={queryClient} client={trpcClient}>
      <QueryClientProvider client={queryClient}>
        <AiAnswersFormContext.Provider value={mockAiAnswersFormContextValue}>
          <AiImprovementsContext.Provider
            value={{ ...mockContextValue, ...overrides }}
          >
            <AiImprovementsList />
          </AiImprovementsContext.Provider>
        </AiAnswersFormContext.Provider>
      </QueryClientProvider>
    </TrpcProvider>
  )
}

describe('<AiImprovementsList />', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders a list of improvements', () => {
    renderWithContext()

    expect(
      screen.getByRole('button', { name: 'First Improvement' })
    ).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: 'Second Improvement' })
    ).toBeInTheDocument()
  })

  it('calls edit handleEditIdChange when clicking an improvement', async () => {
    const user = userEvent.setup()
    renderWithContext()

    await user.click(screen.getByRole('button', { name: 'First Improvement' }))

    expect(mockContextValue.handleEditIdChange).toHaveBeenCalledWith(1)
  })

  it('renders the edit composer ui for an active edit id', () => {
    renderWithContext({ editId: 1 })

    expect(
      screen.queryByRole('button', { name: 'First Improvement' })
    ).not.toBeInTheDocument()
    expect(screen.getByDisplayValue('First Improvement')).toBeInTheDocument()
    expect(screen.getByDisplayValue('First Description')).toBeInTheDocument()
  })

  it('calls handleExternalSourceRemoved when delete is triggered', async () => {
    const user = userEvent.setup()

    renderWithContext({ editId: 1 })

    const deleteButton = screen.getByLabelText('Delete improvement')
    await user.click(deleteButton)

    expect(mockShowConfirmNoty).toHaveBeenCalledTimes(1)

    const call = mockShowConfirmNoty.mock.calls[0]

    call[1].onConfirm()

    await new Promise(resolve => setTimeout(resolve, 10))

    expect(mockHandleExternalSourceRemoved).toHaveBeenCalledWith(1, 'snippet')
  })
})
