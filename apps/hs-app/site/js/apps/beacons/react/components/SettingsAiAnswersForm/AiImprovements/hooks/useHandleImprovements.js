import { useMemo } from 'react'

/**
 * @description
 * Filters the improvements by the snippetIds and then
 * sorts the improvements by the most recent first.
 * If the improvement was updated by a user, it will be sorted by the syncedAt date.
 * Otherwise, it will be sorted by the createdAt date.
 * Then it will return the current improvements and the total number of items.
 * If searchValue is provided, it will filter improvements by name and text.
 */
const useHandleImprovements = ({
  data,
  currentPage,
  snippetIds = [],
  searchValue = '',
  improvementsPerPage = 10,
}) => {
  return useMemo(() => {
    const snippets =
      data?.sources?.filter(source => snippetIds?.includes(source.id)) || []

    const filteredSnippets = searchValue
      ? snippets.filter(snippet => {
          const searchLower = searchValue.toLowerCase()
          return (
            snippet.name?.toLowerCase().includes(searchLower) ||
            snippet.text?.toLowerCase().includes(searchLower)
          )
        })
      : snippets

    const sortedSnippets = [...filteredSnippets].sort((a, b) => {
      const aTimestamp = a.updatedBy != null ? a.syncedAt : a.createdAt
      const bTimestamp = b.updatedBy != null ? b.syncedAt : b.createdAt
      return new Date(bTimestamp) - new Date(aTimestamp)
    })

    const startIndex = (currentPage - 1) * improvementsPerPage
    const endIndex = startIndex + improvementsPerPage

    return {
      currentImprovements: sortedSnippets.slice(startIndex, endIndex),
      totalItems: filteredSnippets.length,
    }
  }, [data, currentPage, improvementsPerPage, searchValue, snippetIds])
}

export default useHandleImprovements
