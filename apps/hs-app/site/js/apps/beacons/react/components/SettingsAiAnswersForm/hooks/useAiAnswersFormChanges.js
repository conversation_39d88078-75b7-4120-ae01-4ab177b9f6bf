import { useAiAnswersFormContext } from '../AiAnswersFormContext'
import isEqual from 'lodash.isequal'
import { useState, useEffect, useRef } from 'react'
import { useLocation } from 'react-router-dom'

/**
 * @description
 * Helper function to check if the current values are different from the previous values
 * for a given tab
 */
const checkTabChanges = (currentValues, prevValues, tab) => {
  if (!currentValues[tab] || !prevValues[tab]) {
    return false
  }

  return Object.keys(currentValues[tab]).some(
    key => !isEqual(currentValues[tab][key], prevValues[tab][key])
  )
}

/**
 * @description
 * Hook to track unsaved changes in the AI Answers form configuration
 * Only tracks changes for the current tab (configure or improvements)
 */
const useAiAnswersFormChanges = () => {
  const [configureHasChanges, setConfigureHasChanges] = useState(false)
  const [improvementsHasChanges, setImprovementsHasChanges] = useState(false)

  const location = useLocation()
  const isConfigureTab = location.pathname.endsWith('/configure')

  const {
    aiAnswersEnabled,
    docsSearchEnabled,
    voiceAndTonePromptText,
    externalSourceIds,
    isSiteSelected,
  } = useAiAnswersFormContext()

  const prevValues = useRef({
    configure: {
      aiAnswersEnabled,
      docsSearchEnabled,
      voiceAndTonePromptText,
      externalSourceIds,
      isSiteSelected,
    },
    improvements: {},
  })

  useEffect(() => {
    const currentValues = {
      configure: {
        aiAnswersEnabled,
        docsSearchEnabled,
        voiceAndTonePromptText,
        externalSourceIds,
        isSiteSelected,
      },
      improvements: {},
    }

    setConfigureHasChanges(
      checkTabChanges(currentValues, prevValues.current, 'configure')
    )
    setImprovementsHasChanges(
      checkTabChanges(currentValues, prevValues.current, 'improvements')
    )

    prevValues.current = currentValues
  }, [
    aiAnswersEnabled,
    docsSearchEnabled,
    voiceAndTonePromptText,
    externalSourceIds,
    isSiteSelected,
  ])

  return isConfigureTab ? configureHasChanges : improvementsHasChanges
}

export default useAiAnswersFormChanges
