import App from '../App'
import SegmentationQuestions from '../react/views/SegmentationQuestions'
import { ReactView } from '@helpscout/brigade'
import React from 'react'

const SegmentationQuestionsViewUpdated = ReactView(
  Marionette.ItemView.extend({
    template() {
      return (
        <SegmentationQuestions
          save={this.save.bind(this)}
          isPartnerSourced={hsGlobal.isPartnerSourced}
        />
      )
    },
    save(data) {
      this.model.set(data)
      this.model.save().then(async () => {
        App.appRouter.navigate('personalization-questions/', {
          trigger: true,
        })
      })
    },
  })
)

export default SegmentationQuestionsViewUpdated
