import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import SegmentationQuestions from './SegmentationQuestions'
import { DROPDOWN_ITEMS, OTHER } from '../../constants/strings.constants'
const { COMPANY_EMPLOYEE, COMPANY_INDUSTRY, USERS, CURRENT_SOLUTION } =
  DROPDOWN_ITEMS

jest.mock('../../utils/trackSignup', () => ({
  trackSignup: jest.fn(),
}))

let props

beforeEach(() => {
  props = { save: jest.fn(), isPartnerSourced: false }
})

afterEach(() => {
  jest.clearAllMocks()
})

describe('Segmentation Question Form', () => {
  test('shows the Segmentation Form', () => {
    render(<SegmentationQuestions {...props} />)
    expect(screen.getByText(/Let.*talk about you/)).toBeInTheDocument()
    expect(
      screen.getByText(/Personalize your Help Scout experience/)
    ).toBeInTheDocument()
    expect(screen.getByTestId('Input.companyName')).toBeInTheDocument()
    expect(
      screen.getByTestId('DropList.companyEmployeeCount')
    ).toBeInTheDocument()
    expect(screen.getByTestId('DropList.companyIndustry')).toBeInTheDocument()
    expect(screen.getByTestId('segmentationNextStep')).toHaveTextContent(
      'Continue'
    )
  })

  test('All required fields are filled Scenario 1', () => {
    const { getByTestId, getByText } = render(
      <SegmentationQuestions {...props} />
    )
    fireEvent.change(getByTestId('Input.companyName'), {
      target: { value: 'My Company' },
    })
    expect(screen.getByTestId('segmentationNextStep')).toHaveClass(
      'is-disabled'
    )
    fireEvent.click(getByTestId('DropList.companyIndustry'))
    fireEvent.click(getByText(COMPANY_INDUSTRY[0]))
    expect(screen.getByTestId('segmentationNextStep')).toHaveClass(
      'is-disabled'
    )
    fireEvent.click(getByTestId('DropList.companyEmployeeCount'))
    fireEvent.click(getByText(COMPANY_EMPLOYEE[0]))
    expect(screen.getByTestId('segmentationNextStep')).not.toHaveClass(
      'is-disabled'
    )
  })

  test('All required fields are filled Scenario 2', () => {
    const { getByTestId, getByText } = render(
      <SegmentationQuestions {...props} />
    )
    fireEvent.change(getByTestId('Input.companyName'), {
      target: { value: 'My Company' },
    })
    expect(screen.getByTestId('segmentationNextStep')).toHaveClass(
      'is-disabled'
    )
    fireEvent.click(getByTestId('DropList.companyIndustry'))
    fireEvent.click(getByText(COMPANY_INDUSTRY[0]))
    expect(screen.getByTestId('segmentationNextStep')).toHaveClass(
      'is-disabled'
    )
    fireEvent.click(getByTestId('DropList.companyEmployeeCount'))
    fireEvent.click(getByText(COMPANY_EMPLOYEE[0]))
    expect(getByTestId('segmentationNextStep')).not.toHaveClass('is-disabled')
  })

  describe('Partner sourced company', () => {
    beforeEach(() => {
      props.isPartnerSourced = true
    })

    test('Hides company name field', () => {
      const { queryByTestId } = render(<SegmentationQuestions {...props} />)

      expect(queryByTestId('Input.companyName')).toBeNull()
    })
  })
})
