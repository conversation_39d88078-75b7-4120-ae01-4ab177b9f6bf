/* eslint jsx-a11y/no-onchange:0 */
import React, { useContext, useEffect } from 'react'
import PropTypes from 'prop-types'

import Input from 'hsds/components/input'

import OnboardingPageContext from 'shared/components/OnboardingPage/context/OnboardingPageContext'

import { useSegmentationQuestions } from '../../hooks'
import { STRINGS, ACTIONS } from '../../constants'
import { SelectInput } from '../'
import CurrentSolutionConditionalFields from '../CurrentSolutionConditionalFields'

const INITIAL_PERCENT_COMPLETED = 20

function SegmentationQuestionsForm({ onSubmit, isPartnerSourced }) {
  const {
    dispatch,
    errors,
    isButtonDisabled,
    percentCompleted,
    setCompanyInputRef,
    values,
  } = useSegmentationQuestions(isPartnerSourced)

  const { setPercentCompleted } = useContext(OnboardingPageContext)

  useEffect(() => {
    setPercentCompleted(
      INITIAL_PERCENT_COMPLETED +
        ((100 - INITIAL_PERCENT_COMPLETED) / 100) * percentCompleted
    )
  }, [percentCompleted, setPercentCompleted])

  const { DROPDOWN_ITEMS } = STRINGS

  const submitForm = e => {
    e.preventDefault()

    dispatch({ type: ACTIONS.IS_SUBMITTING, value: true })
    onSubmit(values)
  }

  return (
    <form onSubmit={submitForm}>
      <fieldset>
        {!isPartnerSourced && (
          <Input
            inputRef={setCompanyInputRef}
            data-cy="Input.companyName"
            data-testid="Input.companyName"
            data-tracking="Registration.Segmentation.companyName"
            id="companyName"
            label="Company Name"
            onChange={value => dispatch({ type: ACTIONS.COMPANY_NAME, value })}
            placeholder="Scout's Club"
            value={values.companyName}
            state={errors.companyName ? 'error' : 'default'}
            errorMessage={errors.companyName}
          />
        )}
        <SelectInput
          id="companyIndustry"
          label="Company Industry"
          items={DROPDOWN_ITEMS.COMPANY_INDUSTRY}
          placeholder="Select one"
          onChange={value =>
            dispatch({ type: ACTIONS.COMPANY_INDUSTRY, value })
          }
        />
        <SelectInput
          id="companyEmployeeCount"
          label="Company Size"
          items={DROPDOWN_ITEMS.COMPANY_EMPLOYEE}
          placeholder="Select one"
          onChange={value =>
            dispatch({ type: ACTIONS.COMPANY_EMPLOYEE_COUNT, value })
          }
        />
      </fieldset>
      <button
        aria-disabled={isButtonDisabled}
        className={`button${isButtonDisabled ? ' is-disabled' : ''}`}
        data-cy="segmentationNextStep"
        data-testid="segmentationNextStep"
        data-tracking="Registration.Segmentation.submit"
        disabled={isButtonDisabled}
        id="segmentationNextStep"
        type="submit"
      >
        {values.isSubmitting ? 'Loading...' : 'Continue'}
      </button>
    </form>
  )
}

export default SegmentationQuestionsForm

SegmentationQuestionsForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isPartnerSourced: PropTypes.bool.isRequired,
}
