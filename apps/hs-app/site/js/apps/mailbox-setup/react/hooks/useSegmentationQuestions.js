import { ACTIONS, STRINGS } from '../constants'
import { useReducer, useRef, useEffect } from 'react'

const {
  COMPANY_EMPLOYEE_COUNT,
  COMPANY_INDUSTRY,
  COMPANY_NAME,
  IS_SUBMITTING,
} = ACTIONS
const { SOLUTION_HELPDESK, OTHER } = STRINGS

// companyName and currentHelpDesk are required but not displayed
const initialState = {
  companyEmployeeCount: '',
  companyIndustry: '',
  companyName: '',
  currentHelpDesk: '',
  currentSolution: '',
  isSubmitting: false,
}

const reducer = (state, action) => {
  switch (action.type) {
    case COMPANY_EMPLOYEE_COUNT:
    case COMPANY_INDUSTRY:
    case COMPANY_NAME:
      return {
        ...state,
        [action.type]: action.value,
      }
    case IS_SUBMITTING:
      return {
        ...state,
        [action.type]: action.value,
      }
    default:
      return state
  }
}

const MAX_INPUT_LENGTH = 60

export const useSegmentationQuestions = isPartnerSourced => {
  const [values, dispatch] = useReducer(reducer, initialState)

  const companyInputRef = useRef()
  const setCompanyInputRef = ref => (companyInputRef.current = ref)

  useEffect(() => {
    companyInputRef?.current?.focus()
  }, [])

  const isCompanyNameValid = values.companyName.length <= MAX_INPUT_LENGTH

  const requiredValues = {
    companyEmployeeCount: values.companyEmployeeCount,
    companyIndustry: values.companyIndustry,
  }

  if (!isPartnerSourced) {
    // The company name is not required for partner sourced companies
    requiredValues.companyName = values.companyName
  }

  const requiredValuesKeys = Object.keys(requiredValues)

  const percentCompleted =
    (100 / requiredValuesKeys.length) *
    requiredValuesKeys.reduce(
      (arr, key) =>
        arr +
        (!(key === 'companyName' && !isCompanyNameValid) &&
        requiredValues[key].trim().length > 0
          ? 1
          : 0),
      0
    )

  const isButtonDisabled = percentCompleted < 100 || values.isSubmitting

  function getValidationErrors() {
    const errors = {}
    if (!isCompanyNameValid) {
      errors.companyName = 'Company name must be less than 60 characters'
    }
    return errors
  }

  return {
    dispatch,
    errors: getValidationErrors(),
    isButtonDisabled,
    percentCompleted,
    setCompanyInputRef,
    values,
  }
}
