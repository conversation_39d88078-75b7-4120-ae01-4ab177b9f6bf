name: Run Cypress tests against Stack

on:
  workflow_call:
    inputs:
      currents-project-id:
        required: true
        type: string
        description: ID of the project in Currents.dev
      sorry-cypress-project-id:
        required: true
        type: string
        description: ID of the project in Sorry-Cypress - it's equal to the project name
      cypress-tag:
        type: string
        required: false
        description: A tag used for filtering of Cypress tests
      tags:
        required: false
        type: string
        description: Tag for the run that shows either in Currents or Sorry-Cypress UI
      environment:
        required: false
        type: string
        description: list of coma separated key=value items that will be passed to Stack, it's meant to override docker image tags
      commit-info-author:
        required: false
        type: string
        description: Github user triggering the tests - it will be read from GH context if not set
      commit-info-branch:
        required: false
        type: string
        description: Github branch triggering the test - it will be read from GH context if not set
      commit-info-message:
        required: false
        type: string
        description: Github commit message triggering the test  - it will be read from GH context if not set
      commit-info-sha:
        required: false
        type: string
        description: Github commit sha triggering the test  - it will be read from GH context if not set
      commit-info-remote:
        required: false
        type: string
        description: Github remote (repository) triggering the test  - it will be read from GH context if not set
      hs-stack-ref:
        default: main
        type: string
        description: Git reference (sha, tag, branch, etc.) for the version of hs-stack to use in the test run
      cypress-tests-ref:
        default: main
        type: string
        description: Git reference (sha, tag, branch, etc.) for the version of cypress tests to run
    secrets:
      HELPSCOUT_BOT_ACCESS_TOKEN:
        required: true

env:
  COMPOSE_FILE: hs-stack/compose/docker-compose.yml:hs-stack/compose/overrides/for-slow-cpus.yml:cypress-tests/compose-cypress-gha.yml
  COMPOSE_ENV_FILES: hs-stack/compose/.env.lock
  CYPRESS_RUNNER_IMAGE_TAG: branch-main
  S3_BASE_LINK: https://s3.sumoci.net/test-artifacts/${{ github.repository }}/${{ github.run_id }}_${{ github.run_attempt }}/cypress/

jobs:
  fix-versions:
    runs-on: [ hs-small ]
    outputs:
      stack-sha: ${{ steps.commits.outputs.stack }}
      stack-artifact: ${{ steps.stack-artifact.outputs.artifact-url }}
      cypress-sha: ${{ steps.commits.outputs.cypress }}
      cypress-artifact: ${{ steps.cypress-artifact.outputs.artifact-url}}
    steps:
      - name: checkout stack
        uses: actions/checkout@v4
        with:
          path: hs-stack
          ref: ${{ inputs.hs-stack-ref }}
          repository: helpscout/hs-stack
          token: ${{ secrets.HELPSCOUT_BOT_ACCESS_TOKEN }}

      - name: checkout cypress tests when called from other repositories
        if: ${{ github.repository != 'helpscout/hs-app-cypress-tests' }}
        uses: actions/checkout@v4
        with:
          path: cypress-tests
          ref: ${{ inputs.cypress-tests-ref }}
          repository: helpscout/hs-app-cypress-tests
          token: ${{ secrets.HELPSCOUT_BOT_ACCESS_TOKEN }}

      - name: checkout cypress tests when called from this repository
        if: ${{ github.repository == 'helpscout/hs-app-cypress-tests' }}
        uses: actions/checkout@v4
        with:
          path: cypress-tests
          repository: helpscout/hs-app-cypress-tests
          token: ${{ secrets.HELPSCOUT_BOT_ACCESS_TOKEN }}

      - name: set commit shas for later
        id: commits
        run: |
          ls -l
          echo "$(pwd)"
          echo "cypress=$(git -C ${{ github.workspace }}/cypress-tests rev-parse --verify HEAD)" >> "$GITHUB_OUTPUT"
          echo "stack=$(git -C ${{ github.workspace }}/hs-stack rev-parse --verify HEAD )" >> "$GITHUB_OUTPUT"

      - uses: actions/upload-artifact@v4
        id: stack-artifact
        with:
          name: hs-stack
          path: hs-stack/
          include-hidden-files: 'true'

      - uses: actions/upload-artifact@v4
        id: cypress-artifact
        with:
          name: cypress-tests
          path: cypress-tests/
          include-hidden-files: 'true'

  cypress-run:
    needs: [ fix-versions ]
    name: runner ${{ matrix.runner }}
    outputs:
      url: ${{ steps.cypress-run.outputs.url }}
    strategy:
      matrix:
        runner: [ 1, 2, 3, 4, 5, 6, 7, 8 ]
      fail-fast: false
    runs-on: [ hs-4xlarge-x86-on-demand-storage ]
    timeout-minutes: 60
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: hs-stack
          path: hs-stack

      - uses: actions/download-artifact@v4
        with:
          name: cypress-tests
          path: cypress-tests

      - name: set environment from the inputs
        env:
          ENVIRONMENT: ${{ inputs.environment }}
        run: |
          IFS=',' read -ra ENV <<< "${{ env.ENVIRONMENT }}"
          for e in "${ENV[@]}"; do
            echo "$e" >> "$GITHUB_ENV"
          done

      - run: mkdir cypress-stack

      - name: pull images
        run: docker compose pull cypress-tests --include-deps --quiet

      - name: store stack config
        run: docker compose config > cypress-stack/stack-configuration.txt

      - name: start cypress
        run: |
          set -o pipefail #fail the step if docker compose returns non-zero exit code
          docker compose run cypress-tests 2>&1 | tee cypress.log
        env:
          BUILD_ID: "${{ github.run_id }}-${{ github.run_attempt }}"
          COMMIT_INFO_AUTHOR: ${{ inputs.commit-info-author || github.actor }}
          COMMIT_INFO_BRANCH: ${{ inputs.commit-info-branch || github.ref_name }}
          COMMIT_INFO_MESSAGE: ${{ inputs.commit-info-message || github.event.head_commit.message }}
          COMMIT_INFO_REMOTE: ${{ inputs.commit-info-remote || format('{0}/{1}', github.server_url, github.repository) }}
          COMMIT_INFO_SHA: ${{ inputs.commit-info-sha || github.sha }}
          CURRENTS_DEV_KEY: ${{ secrets.CURRENTS_RECORD_KEY || 'for-dependabot' }}
          CURRENTS_PROJECT_ID: ${{ inputs.currents-project-id }}
          CYPRESS_TAG: ${{ inputs.cypress-tag }}
          EXTERNAL_TAG: ${{ inputs.tags }}
          RT_PREFIX: "${{ github.run_id }}-${{ github.run_attempt }}-${{ matrix.runner }}"
          SORRY_CYPRESS_PROJECT_ID: ${{ inputs.sorry-cypress-project-id }}

      - name: store cypress run url
        if: ${{ always() }}
        id: cypress-run
        run: | 
          echo "url=$(grep -oP "(?<=Recorded Run: ).*" cypress.log)" >> "$GITHUB_OUTPUT"

      - name: store stack logs
        if: ${{ always() }}
        run: docker compose logs > cypress-stack/stack-logs.txt

      - name: upload logs
        if: ${{ always() }}
        uses: helpscout/github-actions-workflows/actions/aws/upload-to-s3@main
        with:
          source-path: cypress-stack
          additional-options: --recursive
          destination-path: cypress/runner-${{ matrix.runner }}
          add-job-summary: false

      - name: upload cypress debug dump
        if: ${{ hashFiles('cypress-tests/dump') != '' }}
        uses: helpscout/github-actions-workflows/actions/aws/upload-to-s3@main
        with:
          source-path: cypress-tests/dump/
          additional-options: --recursive
          destination-path: cypress/runner-${{ matrix.runner }}/dump
          add-job-summary: false

      - name: add summary for failed runner
        if: ${{ failure() && !cancelled() }}
        run: |
          echo "## 💩 Runner ${{ matrix.runner }} failed, runner specific logs are here [here](${{ env.S3_BASE_LINK }}/runner-${{ matrix.runner }}/)" >> .runners-summary.env

      - name: add summary for happy runner
        if: ${{ success() }}
        run: |
          echo "## ✅ Runner ${{ matrix.runner }} was happy, runner specific logs are here [here](${{ env.S3_BASE_LINK }}/runner-${{ matrix.runner }}/)" >> .runners-summary.env

  summary:
    needs: [ cypress-run, fix-versions ]
    if: ${{ always() }}
    runs-on: [ hs-small ]
    steps:
      - name: add global summary
        run: |          
          echo "## 🌲 Cypress" >> $GITHUB_STEP_SUMMARY
          
          [[ -n "${{ needs.cypress-run.outputs.url }}" ]] && echo "Recorded run: ${{ needs.cypress-run.outputs.url }}"  >> $GITHUB_STEP_SUMMARY
          [[ -z "${{ needs.cypress-run.outputs.url }}" ]] && echo "This workflow didn't produce a recorded run, most likely because Stack failed during startup"  >> $GITHUB_STEP_SUMMARY
          echo ""  >> $GITHUB_STEP_SUMMARY        

          [[ -n "${{ inputs.cypress-tag }}" ]] && echo "Cypress was run with tag \`${{inputs.cypress-tag}}\`" >> $GITHUB_STEP_SUMMARY
          [[ -n "${{ inputs.cypress-tag }}" ]] && echo ""  >> $GITHUB_STEP_SUMMARY        
          
          echo "You can find Cypress tests used for this run neatly packaged in the [workflow artifacts](${{ needs.fix-versions.outputs.cypress-artifact }})." >> $GITHUB_STEP_SUMMARY
          echo "We also marked the commit for you [here](https://github.com/helpscout/hs-app-cypress-tests/commit/${{ needs.fix-versions.outputs.cypress-sha }})" >> $GITHUB_STEP_SUMMARY

          echo "## 🥞 Stack" >> $GITHUB_STEP_SUMMARY
          
          [[ -n "${{ inputs.environment }}" ]]  && echo "Stack was run with this environment: \`${{inputs.environment}}\`" >> $GITHUB_STEP_SUMMARY
          [[ -n "${{ inputs.environment }}" ]] && echo ""  >> $GITHUB_STEP_SUMMARY        
                    
          echo "You can find Stack used for this run neatly packaged in the  [workflow artifacts](${{ needs.fix-versions.outputs.stack-artifact }})." >> $GITHUB_STEP_SUMMARY
          echo "We also marked the commit for you [here](https://github.com/helpscout/hs-stack/commit/${{ needs.fix-versions.outputs.stack-sha }})" >> $GITHUB_STEP_SUMMARY

          echo "## 🗒️ Stack logs" >> $GITHUB_STEP_SUMMARY
          
          echo "Stack logs are [here](${{ env.S3_BASE_LINK }})" >> $GITHUB_STEP_SUMMARY

      - name: add summary for runners
        if: ${{ hashFiles('.runners-summary.env') != '' }}
        run: |
          cat .runners-summary.env | sort >> $GITHUB_STEP_SUMMARY
