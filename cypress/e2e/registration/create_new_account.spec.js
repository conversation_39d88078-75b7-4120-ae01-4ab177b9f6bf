import { hasTags } from '../../support/utils'
import { STANDARD_COMPANY, PLUS_COMPANY } from '../../support/constants/plans'

// Always use the CURRENT and ACTIVE plans
const REGISTER_NEW_DEFAULT_PLUS_ACCOUNT_URL = '/members/register/'
const SEGMENTATION_QUESTIONS = '/welcome/segmentation-questions/'

if (hasTags('registration', 'new-accounts', 'hs-app-ui')) {
  describe('[GROWTH]: Registration/Sign up/Current plans', () => {
    it(`Checks a new company can be created with ${STANDARD_COMPANY.NAME} ${STANDARD_COMPANY.ID} plan`, () => {
      cy.visitEmailRegistrationPage(STANDARD_COMPANY.URL)

      cy.getCsrfToken().then(csrfToken => {
        cy.generateNewUser(
          'low',
          `${STANDARD_COMPANY.NAME}${STANDARD_COMPANY.ID}`
        ).then(user => {
          cy.completeProfile(user)
          cy.completeRecaptchaViaAPI(csrfToken)
          cy.visit(SEGMENTATION_QUESTIONS)
          cy.completeSegmentation({
            company: user.company,
            currentSolution: 'Another help desk',
            currentHelpDesk: 'Intercom',
            companyEmployeeCount: '1000+',
            companyIndustry: 'Other',
          })
          cy.createNewMailbox(user)
          cy.url().should('contain', '/views')
        })
      })
    })

    it(`Checks a new company can be created ${PLUS_COMPANY.NAME} ${PLUS_COMPANY.ID} by DEFAULT`, () => {
      cy.visitEmailRegistrationPage(REGISTER_NEW_DEFAULT_PLUS_ACCOUNT_URL)

      cy.getCsrfToken().then(csrfToken => {
        cy.generateNewUser(
          'low',
          `${PLUS_COMPANY.NAME}${PLUS_COMPANY.ID}`
        ).then(user => {
          cy.completeProfile(user)
          cy.completeRecaptchaViaAPI(csrfToken)
          cy.visit(SEGMENTATION_QUESTIONS)
          cy.completeSegmentation({
            company: user.company,
            currentSolution: 'Chat',
            companyEmployeeCount: '11-50',
            companyIndustry: 'eCommerce',
          })
          cy.createNewMailbox(user)
        })
      })
    })
  })
}
