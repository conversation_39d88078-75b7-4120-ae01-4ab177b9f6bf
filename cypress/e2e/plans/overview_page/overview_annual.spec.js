import { hasTags } from '../../../support/utils'
import { STANDARD_COMPANY } from '../../../support/constants/plans'

const YOUR_PLAN_URL = '/members/plan/'
const NUMBER_OF_USERS = 2
const ADDONS_COST = 5
const CREDIT_REMAINING = 330
const ANNUAL_CREDIT = [55, 59]
const CIRCLE_CLASS = 'js-c-chart--med'

if (hasTags('plans', 'overview')) {
  describe('[GROWTH]: Account Owner view of the Plans > Overview page (Annual)', () => {
    beforeEach(() => {
      cy.session(STANDARD_COMPANY, () => {
        cy.log('**--- Log in with the Basic 36 Plan account ---**')
        cy.fixture(STANDARD_COMPANY.LOGINFILE_ANNUAL).then(
          ({ users: { owner } }) => {
            cy.loginByCSRF(owner)
          }
        )
      })
      cy.intercept('POST', '/api/v0/members/toggle-annual-auto-renew').as(
        'toggleAutoRenew'
      )
      cy.visit(YOUR_PLAN_URL)
      cy.window().then(win => {
        const salesTaxPercentage = win.appData.payments.annual.tax.rate * 100
        cy.wrap(salesTaxPercentage).as('salesTaxPercentage')
      })
    })

    it('Checks AO can see the Plan details section (ANNUAL)', () => {
      const proratedInfoLink = `The <a href='https://docs.helpscout.com/article/596-price-and-plans-guide#common' target='_blank'>`
      const proratedInfoText = `prorated</a> amount is based on the number of days left in the billing cycle when the user was added.`

      cy.log(`**--- Check Account's Plan is ${STANDARD_COMPANY.NAME} ---**`)
      cy.getByCy('PlanOverview.Plan.title').should(
        'contain',
        `${STANDARD_COMPANY.NAME} Plan`
      )
      cy.getByCy('PlanOverview.Plan.subtitle').should(
        'contain',
        "Here's how your plan breaks down this month"
      )

      cy.log('**--- Check Prorated users line item ---**')
      cy.getByCy('lineItemRow.proratedUser').should('contain', 'Prorated -')
      cy.getByCy('lineItemRow.ProratedUser.subtitle').should(
        'contain',
        'Users added last billing cycle'
      )
      cy.getByCy('lineItemRow.ProratedUser.popover').should(
        'have.attr',
        'data-content',
        proratedInfoLink + proratedInfoText
      )
      cy.get('[data-cy="lineItemRow.proratedUser"] > a')
        .should('contain', '1 user')
        .click()
      cy.getByCy('totalsModal.heading').should('contain', 'Your Account')
      cy.getByCy('totalsModal.proratedUsers.tab').should(
        'contain',
        'Prorated Users'
      )
      cy.getByCy('totalsModal.proratedUsers.Uheader').should('contain', 'User')
      cy.getByCy('totalsModal.proratedUsers.Cheader').should(
        'contain',
        'Created Date'
      )
      cy.getByCy('totalsModal.proratedUsers.Pheader').should(
        'contain',
        'Prorated Days'
      )
      cy.getByCy('totalsModal.proratedUsers.lineItem')
        .should('contain', 'Prorated User')
        .and('have.attr', 'href', '/users/profile/154811/')
      cy.getByCy('totalsModal.proratedUsers.created').should('be.visible')
      cy.getByCy('totalsModal.proratedUsers.prorated').should('be.visible')
      cy.getByCy('totalsModal.Close.button')
        .click()
        .url()
        .should('include', '/members/plan/')

      cy.log('**--- Check Annual Payment Line Item ---**')
      cy.getByCy('PromoRow.Title').should('contain', 'Annual Payment')
      cy.getByCy('PromoRow.Description').should(
        'contain',
        `$${CREDIT_REMAINING} remaining`
      )
      cy.getByCy('PromoRow.Badge').should('contain', 'credit')
      cy.getByCy('PromoRow.Amount')
        .invoke('text')
        .should(
          'containOneOf',
          ANNUAL_CREDIT.map(credit => `\\(\\$${credit}\\)`)
        )
      console.debug()

      cy.log('**--- Check Total amount and next payment date ---**')
      cy.getByCy('planTotals.amount').should('contain', '$0')
      // we can't count on this date because its calculated dynamically now from the current time to the plan migration
      cy.getByCy('planTotals.nextPayment.annual')
        .invoke('text')
        .should(
          'match',
          /Next annual payment on (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d+, \d+/
        )
    })

    it('Checks AO can see the Billing section (ANNUAL)', function () {
      cy.log('**--- Check Billing Heading ---**')
      cy.getByCy('PlanOverview.Billing.title').should('contain', 'Billing')
      cy.getByCy('PlanOverview.Billing.subtitle').should(
        'contain',
        'Contact, auto-renewal, and payment information'
      )

      cy.log('**--- Check Credit Remaining ---**')
      cy.getByCy('annualBilling.Credit.title').should(
        'contain',
        'Credit Remaining'
      )
      let data = calculateAnnualBill(this.salesTaxPercentage)

      const creditNoteTexts = data.annualSavings.map((savings, index) => {
        return `You’re saving \\$${savings} by paying annually, so nice work! Your next payment of \\$${data.userTotalAnnualWithDiscount[index]} \\(plus sales tax\\) is estimated to be on `
      })

      cy.getByCy('annualBilling.Credit.description').should($note => {
        expect($note.get(0).innerText).to.containOneOf(creditNoteTexts)
      })

      // we can't count on this date because its calculated dynamically now from the current time to the plan migration
      cy.getByCy('annualBilling.Credit.description')
        .invoke('text')
        .should(
          'match',
          /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d+, \d+.[\r\n\s\\n]+You can also make a one-time payment any time./
        )

      cy.log('**--- Check Credit remaining circle ---**')
      cy.get('#js-react-credit-remaining-circle-chart').should('be.visible')
      cy.getByCy('CreditRemainingCircle').and('have.class', CIRCLE_CLASS)
      cy.get('svg').should('contain', 'Credits Remaining')
      cy.getByCy('CreditRemainingCircle.Percent').and(
        'have.class',
        `circle-foreground`
      )

      cy.getByCy('CreditRemainingCircle.Percent')
        .invoke('attr', 'class')
        .should(
          'containOneOf',
          data.creditsRemainingPercentage.map(
            percentage => `percent${percentage.toFixed(0)}`
          )
        )

      cy.log('**--- Check Auto-renew ---**')
      const autoRenew =
        "This means we'll charge the card on file when your credits run low and it's time to make another annual payment."
      cy.getByCy('annualBilling.AutoRenew.title').should(
        'contain',
        'Auto-renew'
      )
      cy.removeNbspCharacters(
        '[data-cy="annualBilling.AutoRenew.enableTitle"]',
        autoRenew
      )

      cy.log('**--- Check Payment Method ---**')
      const paymentCard = '•••• •••• •••• 1111'
      cy.getByCy('PlanOverview.Card.title').should('contain', 'Payment Method')
      cy.removeNbspCharacters(
        '[data-cy="PlanOverview.Card.last4"]',
        paymentCard
      )
    })

    it('Checks the Make a One-time payment modal', function () {
      const annualPaymentNote =
        'When you make an annual payment you are purchasing credits.'
      const annualPaymentNote2 =
        'Credits get used monthly, and if you add users over time they will run out faster.'
      const annualPaymentNote3 = 'Learn more'

      cy.log('**--- Open the make a one-time payment modal ---**')
      cy.getByCy('annualBilling.Credit.link')
        .should('contain', 'make a one-time payment')
        .click()
      cy.getByCy('ModalHeaderV2').should('contain', 'Make an Annual Payment')
      cy.getByCy('annualPayment.Note').should($note => {
        expect($note.get(0).innerText).to.include(annualPaymentNote)
        expect($note.get(0).innerText).to.include(annualPaymentNote2)
        expect($note.get(0).innerText).to.include(annualPaymentNote3)
      })
      cy.getByCy('annualPayment.GetHelp.link').should('contain', 'Learn more')

      cy.log('**--- Check Subtotal ---**')
      let data = calculateAnnualBill(this.salesTaxPercentage)
      cy.getByCy('annualPayment.Subtotal.heading').should(
        'contain',
        'Annual Subtotal'
      )
      cy.getByCy('annualPayment.Subtotal.description').should($note => {
        expect($note.get(0).innerText).to.include(
          `${NUMBER_OF_USERS} users @ $${STANDARD_COMPANY.MONTHLY} per month (${STANDARD_COMPANY.FULLNAME} Plan) plus Mailboxes, Docs, and Add-ons`
        )
      })
      cy.getByCy('annualPayment.Subtotal.amount').should(
        'contain',
        data.subTotalAnnual
      )

      cy.log('**--- Check Savings ---**')
      cy.getByCy('annualPayment.Savings.heading').should(
        'contain',
        'Annual Payment Savings'
      )

      cy.getByCy('annualPayment.Savings.description').should($desc => {
        expect($desc.get(0).innerText).to.containOneOf(
          STANDARD_COMPANY.ANNUAL.map(
            annualPrice => `You pay only \\$${annualPrice} per user per month`
          )
        )
      })

      cy.getByCy('annualPayment.Savings.amount')
        .invoke('text')
        .should(
          'containOneOf',
          data.annualSavings.map(savings => `\\(\\$${savings}\\)`)
        )

      cy.log('**--- Check Sales tax ---**')
      cy.removeNbspCharacters(
        '[data-cy="SalesTaxLineItem.label"]',
        data.salesTaxLineItem
      )

      cy.getByCy('taxTotal')
        .invoke('text')
        .should(
          'containOneOf',
          data.salesTax.map(salesTax => `${salesTax.toFixed(2)}`)
        )

      cy.log('**--- Check Total ---**')
      cy.getByCy('annualPayment.Total.amount')
        .invoke('text')
        .should(
          'containOneOf',
          data.totalAnnual.map(total => `\\$${total.toFixed(2)}`)
        )

      cy.log('**--- Attempt to Make an Annual Payment ---**')
      cy.getByCy('ModalActionFooter').within(() => {
        cy.getByCy('PrimaryButton').should('contain', 'Pay Now')
      })

      cy.log('**--- Close Make an Annual Payment modal ---**')
      cy.get('body').type('{esc}')
      cy.url().should('include', '/members/plan/')
    })

    it('Enable and Disable the Auto-renew toggle', function () {
      let data = calculateAnnualBill(this.salesTaxPercentage)
      const disableAutoRenewModal =
        'When your credits run out, you will move to monthly billing and lose your annual discount.'
      const enableAutoRenewModal =
        "Your annual credits will be purchased automatically when it's time to renew."

      const autoRenewEnabled =
        "This means we'll charge the card on file when your credits run low and it's time to make another annual payment."

      const autoRenewPopover =
        'If auto-renew is disabled, you will return to monthly billing when your annual credits run out.'

      cy.log('**--- Click the Auto-renew info popover ---**')
      cy.getByCy('annualBilling.AutoRenew.popoverTrigger').click()
      cy.get('.popover-content').should('contain', autoRenewPopover)
      cy.getByCy('annualBilling.AutoRenew.popoverTrigger').click()

      cy.log('**--- Disable the Auto-renew switch ---**')
      cy.getByCy('annualBilling.AutoRenew.switch').click()
      cy.getByCy('AnnualAutoRenew.Title').should(
        'contain',
        'Disable Auto-Renewal'
      )
      cy.getByCy('AnnualAutoRenew.Disable.Text').should($text => {
        expect($text.get(0).innerText).to.include(disableAutoRenewModal)
      })
      cy.getByCy('AnnualAutoRenew.Submit')
        .should('contain', 'Continue')
        .click()
        .waitAndAssertStatusCode('toggleAutoRenew', 200)
        .url()
        .should('include', YOUR_PLAN_URL)
      cy.log('**--- Toggle is switched off ---**')
      cy.getByCy('annualBilling.AutoRenew.switch').should(
        'not.have.class',
        'active'
      )
      cy.getByCy('annualBilling.AutoRenew.disableTitle').should($text => {
        expect($text.get(0).innerText).to.containOneOf(
          data.annualSavings.map(
            savings =>
              "Enable auto-renew and you won't have to worry about remembering to make a payment when your credits run low. " +
              `It also ensures that you keep the \\$${savings} annual discount.`
          )
        )
      })

      cy.log('**--- Enable the Auto-renew switch ---**')
      cy.getByCy('annualBilling.AutoRenew.switch').click()
      cy.getByCy('AnnualAutoRenew.Title').should(
        'contain',
        'Enable Auto-Renewal'
      )
      cy.getByCy('AnnualAutoRenew.Enable.Text').should($text => {
        expect($text.get(0).innerText).to.include(enableAutoRenewModal)
      })
      cy.getByCy('AnnualAutoRenew.Submit')
        .should('contain', 'Continue')
        .click()
        .waitAndAssertStatusCode('toggleAutoRenew', 200)
        .url()
        .should('include', YOUR_PLAN_URL)
      cy.log('**--- Toggle is switched on ---**')
      cy.getByCy('annualBilling.AutoRenew.switch').should(
        'have.class',
        'active'
      )
      cy.getByCy('annualBilling.AutoRenew.enableTitle').should($text => {
        expect($text.get(0).innerText).to.include(autoRenewEnabled)
      })
    })
  })
}
const calculateAnnualBill = salesTaxPercentage => {
  const addonsMonth = NUMBER_OF_USERS * ADDONS_COST
  const userTotalMonth = STANDARD_COMPANY.MONTHLY * NUMBER_OF_USERS
  const subTotalAnnual = (userTotalMonth + addonsMonth) * 12
  let annualSavings = []
  let userTotalAnnualWithDiscount = []
  let salesTax = []
  let totalAnnual = []
  let creditsRemainingPercentage = []

  for (let i = 0; i < STANDARD_COMPANY.ANNUAL.length; i++) {
    annualSavings[i] =
      (STANDARD_COMPANY.MONTHLY - STANDARD_COMPANY.ANNUAL[i]) *
      12 *
      NUMBER_OF_USERS
    userTotalAnnualWithDiscount[i] = subTotalAnnual - annualSavings[i]
    salesTax[i] = userTotalAnnualWithDiscount[i] * (salesTaxPercentage / 100)
    totalAnnual[i] = userTotalAnnualWithDiscount[i] + salesTax[i]
    creditsRemainingPercentage[i] = Math.floor(
      (CREDIT_REMAINING / userTotalAnnualWithDiscount[i]) * 100
    )
  }
  const salesTaxLineItem = `Estimated sales tax for 98101 (${salesTaxPercentage.toFixed(
    2
  )}%)`
  const annualBill = {
    subTotalAnnual,
    annualSavings,
    userTotalAnnualWithDiscount,
    salesTax,
    totalAnnual,
    salesTaxLineItem,
    creditsRemainingPercentage,
  }
  return annualBill
}
