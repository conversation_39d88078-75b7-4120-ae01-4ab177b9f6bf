import { hasTags } from '../../../support/utils'
import {
  STANDARD_COMPANY,
  PLUS_COMPANY,
  PRO_COMPANY,
} from '../../../support/constants/plans'

const AVAILABLE_PLANS_URL = '/members/available-plans/'
let ownerData

if (hasTags('plans', 'available-plans')) {
  describe('[GROWTH]: Plans page - Available and current plans', () => {
    before(() => {
      cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
        ownerData = data
      })
    })

    beforeEach(() => {
      cy.session(STANDARD_COMPANY, () => {
        cy.log('**--- Log in with the Standard Plan account ---**')
        cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
          ownerData = data
          cy.loginByCSRF(data.users.owner)
        })
      })
      cy.visit(AVAILABLE_PLANS_URL)
    })

    it('Checks current and available plan cards', () => {
      cy.getByCy('PricingTable.Heading')
        .invoke('text')
        .should(
          'match',
          /Choose your Help Scout plan|Choose a current Help Scout plan/
        )
      cy.getByCy('PricingTable.Subheading').should(
        'contain',
        'All of them come with a 30-day money back guarantee'
      )

      cy.log('**--- Check Billing frequency options ---**')
      cy.getByCy('PricingTable.Frequency')
        .should('contain', 'Monthly')
        .and('contain', 'Annual')

      checkCardsPrices('MONTHLY', 'BillingCard.Monthly')
      checkCardsPrices('ANNUAL', 'BillingCard.Annual')

      checkCardsHeading(STANDARD_COMPANY.NAME)
      checkCardsHeading(PLUS_COMPANY.NAME)
      checkCardsHeading(PRO_COMPANY.NAME)

      cy.log('**--- Check Features Shown ---**')
      const featureListLengths = [
        Cypress.$(`[data-cy="StandardPlan.List"]>li`).length,
        Cypress.$(`[data-cy="PlusPlan.List"]>li`).length,
        Cypress.$(`[data-cy="ProPlan.List"]>li`).length,
      ]
      featureListLengths.forEach($list => {
        expect($list, 'Features missing from plan card').to.be.at.least(1)
      })

      checkChangePlansButtons()
    })

    it('Checks links in the page footer', () => {
      cy.log('**--- Check Plan FAQs link value ---**')
      cy.getByCy('finePrint.FAQs')
        .should('have.text', 'Plan FAQs')
        .and('be.visible')

      cy.log('**--- Check Delete Account link value ---**')
      cy.getByCy('Link').should('have.text', 'Delete Account').and('be.visible')

      cy.log('**--- Check Manage Users link value ---**')
      cy.getByCy('finePrint.Users')
        .should('contain', 'Manage Users')
        .and('be.visible')
      cy.getByCy('finePrint.Users').click().url().should('include', '/users/')
    })

    it('Check user can Contact Us about the Pro plan price', () => {
      const companyMessage = `Hi Help Scout, Could you tell me more about the Pro plan? Thanks, ${ownerData.users.owner.ownerName} (${ownerData.name})`
      cy.getByCy('MemberPlan.Plan.pro')
        .click({ force: true })
        .getBeaconIframe()
        .find(`[name="subject"]`)
        .should('have.value', "I'm interested in the Pro plan")
        .should('be.visible')
        .getBeaconIframe()
        .find('#Input2')
        .should('have.value', companyMessage)
    })
  })
}

const checkCardsPrices = (billingFrequency, element) => {
  cy.log(`**--- Check ${billingFrequency} Prices ---**`)
  cy.getByCy(element).first().click()

  if (billingFrequency === 'MONTHLY') {
    cy.getByCy('StandardPlan.Price').should('contain', STANDARD_COMPANY.MONTHLY)
    cy.getByCy('PlusPlan.Price').should('contain', PLUS_COMPANY.MONTHLY)
    cy.getByCy('ProPlan.Price').should('contain', PRO_COMPANY.MONTHLY)
  } else {
    cy.getByCy('StandardPlan.Price')
      .invoke('text')
      .should('containOneOf', STANDARD_COMPANY.ANNUAL)
    cy.getByCy('PlusPlan.Price')
      .invoke('text')
      .should('containOneOf', PLUS_COMPANY.ANNUAL)
    cy.getByCy('ProPlan.Price').should('contain', PRO_COMPANY.ANNUAL)
  }
}

const checkCardsHeading = plan => {
  cy.log(`**--- Check ${plan} Prices ---**`)
  cy.getByCy(`${plan}Plan.Heading`).should('have.text', `${plan}`)

  if (plan === PRO_COMPANY.NAME) {
    cy.getByCy(`${plan}Plan.Frequency`).should(
      'have.text',
      'per user per month'
    )
  } else {
    cy.getByCy(`${plan}Plan.Frequency`).should(
      'have.text',
      'per user per month'
    )
    cy.getByCy(`${plan}Plan.Price`)
      .should('have.css', 'content')
      .and('match', /$/)
  }
}

const checkChangePlansButtons = () => {
  cy.log('**--- Check Changing plans buttons ---**')
  cy.getByCy('MemberPlan.Plan.standard').should(
    'have.text',
    'Your Current Plan'
  )
  cy.getByCy('MemberPlan.Plan.plus').should('have.text', 'Upgrade to Plus')
  cy.getByCy('MemberPlan.Plan.pro').should('have.text', 'Contact Us')
}
