import { withTags } from '../../support/utils'
import { PLUS_COMPANY } from '../../support/constants/plans'
import { DESKTOP_1600x1200 } from '../../support/constants/browserSizes'

withTags('properties', 'createRemove', () => {
  beforeEach(() => {
    cy.session(PLUS_COMPANY, () => {
      cy.log('**--- Log in with the Plus 41 Plan account ---**')
      cy.fixture(PLUS_COMPANY.LOGINFILE).then(({ users: { owner } }) => {
        cy.loginByCSRF(owner)
      })
    })
    cy.viewport(
      DESKTOP_1600x1200.BROWSER_WIDTH,
      DESKTOP_1600x1200.BROWSER_HEIGHT
    )
    cy.intercept('POST', '/api/v0/customer-properties*').as('createProperty')

    cy.visit('/settings/properties')
  })

  describe('[APPS]: Check Properties blank slate page', () => {
    const HOW_TO_CREATE_CUSTOM_PROPERTIES_ARTICLE_ID =
      '5dd8646104286364bc921b4f'

    it('Check blank slate layout', () => {
      cy.log('**--- Properties blank slate page ---**')
      cy.getByCy('Property.Blank.Title').should('have.text', 'Properties')
      cy.getByCy('Property.Blank.Subtitle')
        .invoke('text')
        .then(text => {
          expect(text.trim()).equal(
            'Properties make important customer relationship dataavailable in the conversation sidebar.'
          )
        })

      cy.getByCy('Property.Blank.List')
        .children()
        .should($list => {
          expect($list).to.have.length(3)
          expect($list.eq(0)).to.contain('Get context right away')
          expect($list.eq(1)).to.contain('Reply to conversations faster')
          expect($list.eq(2)).to.contain(
            'Auto-update properties with Beacon or the API'
          )
        })

      cy.get('.icon-check').should('have.length', '3')

      cy.getByCy('Property.Blank.LearnMore').should('have.text', 'Learn more')
      cy.getByCy('Property.Blank.Image').should('be.visible')
      cy.getByCy('Property.Blank.CreateProperty').should(
        'have.text',
        'Create Property'
      )
    })

    it('Check Learn more links within modal', () => {
      cy.getByCy('Property.Blank.CreateProperty')
        .should('have.text', 'Create Property')
        .click()

      cy.getByCy('Modal').should('contain', 'Create New Property')
      cy.getByCy('Property.CreateForm.Slug.help').should(
        'include.text',
        'This is used to update properties via the API and Beacon'
      )
      cy.get('[data-cy="Property.CreateForm.Slug.help"] a').should(
        'include.text',
        'Learn more'
      )

      cy.get('[data-cy="Property.CreateForm.Type.help"] a').should(
        'include.text',
        'Learn more'
      )
    })
  })
})
