import { hasTags } from '../../support/utils'
import {
  getMessagesAccount,
  getMessagesBeaconConfig,
} from '../../support/ciUtils'
import { DESKTOP_1600x1200 } from '../../support/constants/browserSizes'

// hack to satisfy cy.session() since it needs and object but we're using a function
const STRING = 'string'

if (hasTags('beacon-messages', 'messages-unfurling', 'hs-app-ui')) {
  context('[MESSAGES]: Beacon Messages Reports E2E', () => {
    let beaconId
    let beaconE2E

    const messageName = 'CYPRESS NPS - LEAVE ALONE!!'

    const timeRange = {
      last7: 'Last 7 days',
      lastWeek: 'Last week',
      thisMonth: 'This month',
      last30: 'Last 30 days',
      lastMonth: 'Last month',
      allTime: 'All time',
    }

    const badSearch = 'xxx'

    before(() => {
      cy.intercept('GET', `/api/v0/messages/?ts=*`).as('getAllMessages')
      cy.fixture(getMessagesBeaconConfig())
        .as('beaconConfig')
        .then(data => {
          beaconId = data.id
        })
    })

    beforeEach(() => {
      cy.session(STRING, () => {
        cy.log('**--- Log in with the Messages account ---**')
        cy.fixture(getMessagesAccount()).then(data => {
          beaconE2E = data
          const {
            owner: { email, password },
          } = beaconE2E.users
          cy.loginByCSRF({ email, password })
        })
      })
      cy.viewport(
        DESKTOP_1600x1200.BROWSER_WIDTH,
        DESKTOP_1600x1200.BROWSER_HEIGHT
      )

      cy.intercept('POST', `/api/v0/reports/messages**`).as('getReportData')
      cy.intercept('GET', `/api/v0/messages/?ts=*`).as('getAllMessages')

      cy.intercept('GET', `/api/v0/messages/*?ts=*`).as('getMessage')

      cy.on('uncaught:exception', (err, runnable) => {
        expect(err.message).to.include('ResizeObserver')
        return false
      })

      cy.visit(`/messages`).waitAndAssertStatusCode('getAllMessages', 200)
      cy.contains(messageName)
      cy.log('**--- Click an existing Message with report data ---**')
        .getByCy('SortableItem')
        .should('have.length', 9)
        .getByCy('SortableItem')
        .contains(messageName)
        .click()
      cy.waitAndAssertStatusCode('getReportData', 200)
    })

    it('Can display response comments for a previously created (active) Message', function () {
      cy.log('**--- Change time to All Time ---**')
        .getByCy('Input.Message_Date_Filter')
        .click()
        .get('li')
        .contains(timeRange.allTime)
        .click()

      cy.log(
        '**--- Check the Messages response count + preview displays correctly ---**'
      )
        .get('[data-testid="survey-comment"]')
        .should('have.length.above', 0)
        .should('contain', 'It was such a great experience')

      cy.log('Can display the Message responses')
      cy.getByCy('responses-nav-tab').should('contain', 'Responses').click()

      cy.getByCy('Heading').should('contain', 'Responses').and('contain', '1')

      cy.log('Can yield no results with a bad search term')
      cy.log('**--- Enter bad search term ---**')
        .get('[data-cy=Input][name="search-responses"]')
        .type(badSearch)

      cy.log('**--- Check no results blank slate ---**')
        .getByCy('PageContent')
        .should(
          'contain',
          'Couldn\'t find anything for "xxx". Care to try again?'
        )

      cy.getByCy('Heading').should('contain', 'Responses').and('contain', '0')

      cy.log('**--- Remove bad search term ---**').getByCy('IconButton').click()

      cy.getByCy('PageContent').should(
        'not.contain',
        'Couldn\'t find anything for "xxx". Care to try again?'
      )

      cy.getByCy('Heading').should('contain', 'Responses').and('contain', '1')

      cy.log('Can yield no responses when filtering with a wrong score')
      cy.getByCy('Button').contains('Filter').click()

      cy.getByCy('DropList.select').find('li').contains('0').click()

      cy.log('**--- Check no results blank slate ---**')
        .getByCy('PageContent')
        .should(
          'contain',
          "We couldn't find any results matching those filters."
        )

      cy.getByCy('Heading').should('contain', 'Responses').and('contain', '0')

      cy.log('**--- Remove bad filter ---**')
        .getByCy('Button')
        .contains('Clear')
        .click()

      cy.getByCy('PageContent').should(
        'not.contain',
        "We couldn't find any results matching those filters."
      )

      cy.getByCy('Heading').should('contain', 'Responses').and('contain', '1')

      cy.log('Returns the correct response when filtering with a good score')
      cy.getByCy('Button').contains('Filter').click()

      cy.getByCy('DropList.select').find('li').contains('10').click()

      cy.getByCy('TableBody')
        .find('tr')
        .should('have.length', '1')
        .and('contain', 'It was such a great experience')

      cy.log('**--- Remove filter ---**')
        .get('[data-cy="PageHeader"] [data-cy="Button"]')
        .contains('Clear')
        .click()

      cy.log('Can start a convo with the customer who responded')
      cy.getByCy('DropList.MeatButtonToggler')
        .last()
        .click()
        .get('.DropListItem')
        .should('contain', 'Start conversation')
    })
  })
}
