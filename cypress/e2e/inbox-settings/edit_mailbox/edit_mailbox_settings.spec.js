import { hasTags } from '../../../support/utils'
import { DESKTOP_1280x1024 } from '../../../support/constants/browserSizes'
import faker from 'faker'
import { PLUS_COMPANY } from '../../../support/constants/plans'
import { getMailboxDefault2ndLevelDomain } from '../../../support/ciUtils'

const EDIT_MAILBOX_SETTINGS = '/settings/inbox/2602/'
let mailboxAddress, connectedEmail, mailboxName, owner
const notFreeEmail = 'example.com'

if (hasTags('mailbox-settings')) {
  context(`[INBOX]: Ensure you can edit your Inbox's settings`, () => {
    const randomCompany = faker.company.companyName()
    const randomNumber = faker.random.number()
    const randomEmail = `${randomNumber}@${randomNumber}.com`
    const randomAlias = `${randomNumber}.alias@${randomNumber}.com`

    before(() => {
      cy.fixture(PLUS_COMPANY.LOGINFILE_EMPIRE).then(data => {
        mailboxName = data.mailboxes.imperialGuard.name
        mailboxAddress = getMailboxDefault2ndLevelDomain(data)
        connectedEmail = data.mailboxes.imperialGuard.connectedEmail
        owner = data.users.owner
      })
    })

    beforeEach(() => {
      cy.session(PLUS_COMPANY.LOGINFILE_EMPIRE, () => {
        cy.log('**--- Log in with the Empire account ---**')
        cy.loginByCSRF(owner)
      })
      cy.intercept('PUT', '/api/v0/mailboxes/*').as('saveMailboxSettings')
      cy.intercept('POST', '/settings/mailbox/*/alias*').as('addAlias')
      cy.intercept('DELETE', '/settings/mailbox/*/alias/*').as('deleteAlias')
      cy.viewport(
        DESKTOP_1280x1024.BROWSER_WIDTH,
        DESKTOP_1280x1024.BROWSER_HEIGHT
      )
      cy.log('**--- Visit the Edit Inbox settings homepage ---**')
      cy.visit(EDIT_MAILBOX_SETTINGS)
    })

    after(() => {
      cy.log('**--- Rollback to previous inbox name ---**')
      editMailboxName(mailboxName)
    })

    it('Checks and updates the Inbox section', () => {
      cy.log('**--- Verify Inbox > Name is correct ---**')
      cy.get(
        '[data-cy="EditMailbox.MailboxNameAndEmail.Heading"] [data-cy="Heading"]'
      ).should('have.text', 'Inbox')
      cy.get(
        '[data-cy="EditMailbox.MailboxNameAndEmail.Heading"] .c-PageHeader__subtitle'
      ).should('have.text', 'Manage properties for this Inbox')
      cy.get('[for="name"]').should('contain', 'Inbox Name')
      cy.get('[for="mbx-address"]').should('contain', 'Inbox Address')
      cy.getByCy('EditMailbox.MailboxNameAndEmail.Address').should(
        'have.value',
        mailboxAddress
      )
      cy.get('[for="email"]').should('contain', 'Connected Email Address')
      cy.getByCy('EditMailbox.MailboxNameAndEmail.ConnectedAddress').should(
        'have.value',
        connectedEmail
      )

      cy.log('**--- Update the name of the Inbox with a random value ---**')
      editMailboxName(randomCompany)
    })

    it('Checks and updates the Default Settings section', () => {
      cy.log('**--- Update From name dropdown to Custom Name ---**')
        .getByCy('DropList.SelectTagToggler')
        .eq(0)
        .click()

      cy.get('[data-tippy-root]')
        .find('li')
        .contains('Custom Name')
        .scrollIntoView()
        .click()

      cy.getByCy('EditMailbox.DefaultSettings.FromNameCustom')
        .clear()
        .type(`${randomCompany}${randomNumber}`)

      cy.log('**--- Set status from Closed to Pending ---**')
        .getByCy('DropList.SelectTagToggler')
        .eq(1)
        .click()

      cy.get('[data-tippy-root]')
        .find('li')
        .contains('Pending')
        .scrollIntoView()
        .click()

      cy.log('**--- Set assignee from Anyone to Person Replying ---**')
        .getByCy('DropList.SelectTagToggler')
        .eq(2)
        .click()

      cy.get('[data-tippy-root]')
        .find('li')
        .contains('Person Replying')
        .scrollIntoView()
        .click()

      cy.getByCy('MailboxBccForm.AutoBccToggleID').then($autoBccToggle => {
        if ($autoBccToggle.hasClass('is-checked')) {
          cy.log('**--- Bcc toggle was turned ON so update Bcc email ---**')
        } else {
          cy.log('**--- Bcc toggle was turned OFF so enable it first ---**')
          cy.get('input[name=autoBccToggle]').click({ force: true })
        }
        cy.getByCy('EditMailbox.DefaultSettings.AutoBccList')
          .clear()
          .type(randomEmail)
      })

      cy.log('**--- Update Default Settings ---**')
      cy.getByCy('MailboxSettings.Save.button')
        .should('contain', 'Save')
        .click({ force: true })
      cy.get('[data-testid=NotyElement').should(
        'contain',
        'Inbox settings have been saved'
      )
      cy.waitAndAssertStatusCode('saveMailboxSettings', 200)
      cy.reload()

      cy.log('**--- Check updates were saved correctly ---**')
      cy.getByCy('DropList.SelectTagToggler').eq(0).contains('Custom Name')
      cy.getByCy('EditMailbox.DefaultSettings.FromNameCustom').should(
        'have.value',
        `${randomCompany}${randomNumber}`
      )
      cy.getByCy('DropList.SelectTagToggler').eq(1).contains('Pending')
      cy.getByCy('DropList.SelectTagToggler').eq(2).contains('Person Replying')
      cy.getByCy('EditMailbox.DefaultSettings.AutoBccList').should(
        'have.value',
        randomEmail
      )
    })

    it('Checks and updates the Signature section', () => {
      cy.log('**--- Select job title from variable dropdown ---**')
        .get('select[name=variable]')
        .select('Job Title')

      cy.get('.redactor_editor').should(
        'contain',
        '{%user.jobTitle,fallback=%}'
      )
    })

    it('Checks and updates the Aliases section', () => {
      cy.getByCy('MailboxAliases.ReplyAsAlias.Switch').then(
        $replyAsAliasToggle => {
          if ($replyAsAliasToggle.hasClass('is-checked')) {
            cy.log('**--- Reply As Alias toggle was turned ON ---**')
          } else {
            cy.log('**--- Reply As Alias toggle was turned OFF ---**')
            cy.get('input[name=replyAsAlias]').click({ force: true })
          }
          cy.getByCy('MailboxAliases.ReplyAsAlias.TableLabel').should(
            'contain',
            "You'll be able to reply from Aliases that are confirmed below"
          )
          //We need to start with zero aliases if we want to rerun the tests
          cleanAllAliases()
        }
      )

      cy.log('**--- Add new alias email with random domain (Table) ---**')
      cy.addingAlias(randomAlias)
        .waitAndAssertStatusCode('addAlias', 201)
        .getByCy('EditMailbox.Aliases.NewAliasRow.Email')
        .should('contain', randomAlias)
        .getByCy('MailboxAliases.Table.VerifyLink')
        .should('contain', 'Verify')

      cy.log('**--- Delete alias ---**')
      cy.deleteAlias(randomAlias).waitAndAssertStatusCode('deleteAlias', 204)
      cy.getByCy('EditMailbox.Aliases.NewAliasRow.Email').should(
        'not.exist',
        randomAlias
      )

      cy.log('**--- Add alias email with same domain (Table) ---**')
      cy.addingAlias(`${randomNumber}@${notFreeEmail}`)
        .waitAndAssertStatusCode('addAlias', 201)
        .getByCy('EditMailbox.Aliases.NewAliasRow.Email')
        .should('contain', `${randomNumber}@${notFreeEmail}`)
      cy.getByCy('EditMailbox.Aliases.Table').within(() => {
        cy.getByCy('Icon').eq(0).should('have.class', 'is-iconName-check')
      })

      cy.log('**--- Add alias email same as inbox email ---**')
      cy.addingAlias(connectedEmail)
        .waitAndAssertStatusCode('addAlias', 400)
        .getByCy('EditMailbox.Aliases.AddNewAlias.duplicated')
        .should('contain', `Alias can't match mailbox email: ${connectedEmail}`)
        .getByCy('EditMailbox.Aliases.NewAliasRow.Cancel')
        .click()
    })
  })
}

const cleanAllAliases = () => {
  cy.get('body').then($body => {
    if (
      $body.find('[data-cy="EditMailbox.Aliases.NewAliasRow.Delete"]').length >
      0
    ) {
      cy.getByCy('EditMailbox.Aliases.NewAliasRow.Delete').each($el => {
        cy.wrap($el).click()
        cy.get('#simple-modal')
          .contains('Delete alias:')
          .get('.Confirmation_ConfirmButton')
          .click({ force: true })
        cy.waitAndAssertStatusCode('deleteAlias', 204)
      })
    } else {
      cy.log('**--- There are no existing aliases ---**')
    }
  })
}

const editMailboxName = updateMailboxName => {
  cy.log('mailboxName', mailboxName)
  cy.log('updateMailboxName', updateMailboxName)
  cy.getByCy('EditMailbox.MailboxNameAndEmail.Name')
    .click({ force: true })
    .clear()
    .type(updateMailboxName)
  cy.getByCy('MailboxSettings.Save.button')
    .should('contain', 'Save')
    .click({ force: true })
    .waitAndAssertStatusCode('saveMailboxSettings', 200)
  cy.reload()
    .getByCy('EditMailbox.MailboxNameAndEmail.Name')
    .should('have.value', updateMailboxName)
}
