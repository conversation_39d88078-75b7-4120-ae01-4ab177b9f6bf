import { hasTags } from '../../../support/utils'
import { DESKTOP_1280x1024 } from '../../../support/constants/browserSizes'
import { STANDARD_COMPANY } from '../../../support/constants/plans'

const EDIT_MAILBOX_SETTINGS = '/settings/inbox/90899/'
const CHANNEL_SETTINGS_MESSENGER = 'channels-settings/messenger-integrations'
const CHANNEL_SETTINGS_INSTAGRAM = 'channels-settings/instagram-integrations'
let mailboxName

if (hasTags('mailbox-settings')) {
  context(`[GROWTH]: Manage Channel integration in Inbox Settings`, () => {
    before(() => {
      cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
        mailboxName = data.mailboxes.support.name
      })
    })

    beforeEach(() => {
      cy.session(STANDARD_COMPANY.LOGINFILE, () => {
        cy.log('**--- Log in with the Standard Company ---**')
        cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
          cy.loginByCSRF(data.users.owner)
        })
      })

      cy.intercept('GET', '/api/v1/channels/integrations?channel=messenger*', {
        fixture: CHANNEL_SETTINGS_MESSENGER,
      }).as('getMessengerIntegrations')

      cy.intercept('GET', '/api/v1/channels/integrations?channel=instagram*', {
        fixture: CHANNEL_SETTINGS_INSTAGRAM,
      }).as('getInstagramIntegrations')

      cy.intercept('PATCH', '/api/v1/channels/integrations/*', {
        statusCode: 200,
        body: {},
      }).as('patchInstagramIntegrations')

      cy.intercept('DELETE', '/api/v1/channels/integrations/*', {
        statusCode: 204,
        body: {},
      }).as('deleteInstagramIntegrations')

      cy.viewport(
        DESKTOP_1280x1024.BROWSER_WIDTH,
        DESKTOP_1280x1024.BROWSER_HEIGHT
      )

      cy.log('**--- Visiting Edit Inbox Settings page ---**')
      cy.visit(EDIT_MAILBOX_SETTINGS)
    })

    it('Verifies the Channel Settings Section', () => {
      cy.wait('@getMessengerIntegrations')
      cy.wait('@getInstagramIntegrations')
      cy.log('**--- Verifying Channels Settings Heading ---**')
      cy.getByCy('ChannelSettings.Heading').within(() => {
        cy.get('[data-cy="Heading"]').should('have.text', 'Channels')
        cy.get('.c-PageHeader__subtitle').contains(
          'Add customer conversations from various channels into your Inbox'
        )
        cy.get('.c-PageHeader__subtitle > [data-cy="Button"]').contains(
          'button',
          'Learn More'
        )
      })
    })

    it('Verifies Messenger from Facebook Integration', () => {
      cy.fixture(CHANNEL_SETTINGS_MESSENGER).then(data => {
        const [firstPage, secondPage] = data

        cy.get('[data-cy="AccordionSection"][id="1"]').within(() => {
          cy.contains('Messenger from Facebook')
          cy.getByCy('Badge').contains('connected')
          cy.getByCy('AccordionTitle').click()

          cy.getByCy('Scrollable').within(() => {
            cy.contains('Connected Pages')
            cy.getByCy('Button')
              .contains('a', firstPage.name)
              .should(
                'have.attr',
                'href',
                `https://facebook.com/profile.php?id=${firstPage.channelSourceId}`
              )
          })

          cy.getByCy('ChannelSettings.messenger.Disconnect.Button').should(
            'have.text',
            'Disconnect'
          )
        })

        cy.log('**--- Opening Manage Integrations Modal ---**')
        cy.getByCy('ChannelSettings.messenger.Manage.Button')
          .should('have.text', 'Manage')
          .click()
        cy.get('.SimpleModal__ContainerScroll').within(() => {
          cy.contains(`Connect Pages to ${mailboxName}`)
          cy.contains(
            `Select which Facebook Page(s) you'd like to connect to the ${mailboxName} Inbox:`
          )
          cy.get('thead').contains('Facebook Pages')
          cy.get('tbody').within(() => {
            cy.get('tr').should('have.length', 2)
            cy.get('tr').eq(0).contains(firstPage.name)
            cy.get('tr').eq(1).contains(secondPage.name)
          })
        })

        cy.log('**--- Reassigning the second page to this inbox ---**')
        cy.getByCy('Reassign.Button').click()
        cy.getByCy('Reassign.Confirm.Button').click()

        cy.getByCy('Connect.Pages.Button').click()
        cy.wait('@patchInstagramIntegrations')

        cy.log('Successfully connected Inbox')
        cy.get('[data-testid=NotyElement').should(
          'contain',
          `${secondPage.name} has been connected to this Inbox`
        )
      })
    })

    it('Verifies Instagram Integration', () => {
      cy.fixture(CHANNEL_SETTINGS_INSTAGRAM).then(data => {
        const { name } = data[0]

        cy.get('[data-cy="AccordionSection"][id="2"]').within(() => {
          cy.contains('Instagram')
          cy.contains('Route Instagram messages into your Inbox')
        })

        cy.log('**--- Opening Manage Integrations Modal ---**')
        cy.getByCy('AccordionHead.Button').contains('Connect').click()
        cy.get('.SimpleModal__ContainerScroll').within(() => {
          cy.contains(`Connect Account to ${mailboxName}`)
          cy.contains(
            `Select which Instagram Account(s) you'd like to connect to the ${mailboxName} Inbox:`
          )
          cy.get('thead').contains('Instagram Account')
          cy.get('tbody').within(() => {
            cy.get('tr').should('have.length', 1)
            cy.get('tr').eq(0).contains(name)
          })
        })

        cy.log('**--- Assigning the Instagram account to this inbox ---**')
        cy.get('tbody > tr div[data-cy="Checkbox"]').click()
        cy.getByCy('Connect.Account.Button')
          .click()
          .wait('@patchInstagramIntegrations')

        cy.log('Successfully connected Inbox')
        cy.get('[data-testid=NotyElement').should(
          'contain',
          `${name} has been connected to this Inbox`
        )
      })
    })

    it('Disconnects the Messenger Integration', () => {
      cy.getByCy('AccordionSection').first().click()

      cy.getByCy('ChannelSettings.messenger.Disconnect.Button')
        .should('have.text', 'Disconnect')
        .click()

      cy.getByCy('ConfirmDisconnect.Button').click()
      cy.wait('@deleteInstagramIntegrations')
      cy.get('[data-testid=NotyElement').should(
        'contain',
        'Messenger From Facebook has been disconnected from this inbox'
      )
    })
  })
}
