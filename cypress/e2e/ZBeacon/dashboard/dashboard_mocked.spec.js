import { hasTags } from '../../../support/utils'
import { PLUS_COMPANY } from '../../../support/constants/plans'

if (hasTags('beacon-dashboard', 'beacon-dashboard-mocked')) {
  context('[MESSAGES]: Beacon dashboard tests', () => {
    let empire
    const beaconCardData = {
      firstBeaconColor: 'rgb(75, 194, 125)',
      secondBeaconColor: 'rgb(75, 194, 126)',
      thirdBeaconColor: 'rgb(75, 194, 127)',
      disabledTextColor: 'rgb(147, 161, 176)',
      enabledTextColor: 'rgb(49, 67, 81)',
    }

    beforeEach(() => {
      cy.session(PLUS_COMPANY.LOGINFILE_EMPIRE, () => {
        cy.log('**--- Log in with the Empire account ---**')
        cy.fixture(PLUS_COMPANY.LOGINFILE_EMPIRE).then(data => {
          empire = data
          const {
            owner: { email, password },
          } = empire.users
          cy.loginByCSRF({ email, password })
        })
      })
    })

    it('Should load correct dashboard states', () => {
      cy.log('Setup 500 error response')
      cy.intercept('/api/v0/beacons', {
        method: 'GET',
        statusCode: 500,
        body: '',
      })
      cy.intercept('GET', '/api/v0/reports/beacons?*', {
        sessions: { total: 0 },
        aiResolutions: {},
        perBeaconSummary: {},
      }).as('getReports')
      cy.log('Visit Beacon homescreen')
      cy.visit(`settings/beacons/`)
      cy.log('Verify there are no Beacons')
      cy.get('[data-cy=beacon-table-row]').should('not.exist')
      cy.log('Verify hs-app can still be navigable via the header')
      cy.get('header .nav-main').should('be.visible')

      cy.log('Should load the blank slate if no Beacons')
      cy.log('Setup mock empty data response')
      cy.intercept('/api/v0/beacons', {
        method: 'GET',
        fixture: '/beacon/dashboard/no-beacons.json',
      })
      cy.log('Visit Beacon Dashboard')
      cy.visit('/settings/beacons')
      cy.log('Verify 0 Beacons')
      cy.get('[data-cy=beacon-table-row]').should('have.length', 0)
      cy.log('Verify hero and blank slate image is displayed')
      cy.get('[data-cy="noBeaconsPage"]').should('be.visible')
      cy.get('[data-cy="beaconsPromoImage"]')
        .should('have.attr', 'src')
        .and('includes', 'beacon-promo-purple.png')
      cy.log('Verify Create Beacon and Developer Docs buttons are displayed')
      cy.get('[data-cy="createBeaconLink"]')
        .should('contain', 'Create a Beacon')
        .and('have.attr', 'href', '/settings/beacons/new')
        .and('be.visible')
      cy.get('[data-cy="devDocsLink"]')
        .should('contain', 'Developer docs')
        .and('have.attr', 'href', 'https://developer.helpscout.com/beacon-2/')
        .and('be.visible')

      cy.log('should load previously created Beacons')
      cy.log('Setup mock Beacon data response')
      cy.intercept('/api/v0/beacons', {
        method: 'GET',
        fixture: '/beacon/dashboard/full-beacon-list.json',
      })
      cy.log('Visit Beacon Dashboard')
      cy.visit(`settings/beacons/`)
      cy.log('Verify 3 Beacons')
      cy.get('[data-cy=beacon-table-row]').should('have.length', 3)
      cy.log('Verify Beacon Cards display all data')
      cy.get('[data-cy=beacon-table-row]')
        .eq(0)
        .should('contain', 'MESSAGING ONLY AND LAST ACTIVE')
        .should('contain', 'May 8')
      cy.get('[data-cy=beacon-table-row]').eq(1).should('contain', 'DOCS ONLY')
      cy.get('[data-cy=beacon-table-row]')
        .eq(2)
        .should('contain', 'CHAT AND MESSAGING')
    })
  })
}
