import { hasTags } from '../../../support/utils'
import faker from 'faker'
import { DESKTOP_1600x1200 } from '../../../support/constants/browserSizes'
import { PLUS_COMPANY } from '../../../support/constants/plans'

const BEACON_CONFIG = '/beacone2e/beacon-config'

if (hasTags('beacon-settings', 'beacon-config')) {
  context('[MESSAGES]: Beacon Builder config settings', () => {
    let beaconId
    let docsSiteId
    let beaconE2E

    before(() => {
      cy.fixture(BEACON_CONFIG).then(data => {
        beaconId = data.id
        docsSiteId = data.docsSiteId
      })
    })

    beforeEach(() => {
      cy.session(PLUS_COMPANY, () => {
        cy.log('**--- Log in with the BeaconE2E account ---**')
        cy.fixture(PLUS_COMPANY.LOGINFILE_E2EBEACON).then(data => {
          beaconE2E = data
          const {
            owner: { email, password },
          } = beaconE2E.users
          cy.loginByCSRF({ email, password })
        })
      })

      cy.viewport(
        DESKTOP_1600x1200.BROWSER_WIDTH,
        DESKTOP_1600x1200.BROWSER_HEIGHT
      )

      cy.intercept('PUT', `/api/v0/beacons/${beaconId}`).as('saveConfig')
      cy.intercept(`/api/v0/beacons/${beaconId}`, {
        fixture: BEACON_CONFIG,
      }).as('stubBeacon')
      cy.intercept('GET', '/v1/articles/*/popular?ts=*').as(
        'getPopularArticles'
      )
      cy.intercept('GET', '/v1/site/*/articles?articleIds=**').as(
        'getAllArticles'
      )
      cy.intercept('GET', '/settings/mailbox/*/custom-fields.json').as(
        'getCustomFields'
      )
      cy.intercept({
        method: 'GET',
        url: `/api/v0/beacons/${beaconId}/labels**`,
      }).as('beaconLabels')
      cy.intercept('PUT', `/api/v0/beacons/${beaconId}/labels?*`).as(
        'saveBeaconLabels'
      )
    })

    it('Tests the Customise screen can be updated and saved', function () {
      cy.visit(`/settings/beacons/${beaconId}/customize`)

      cy.log('**--- Check initial state and update config ---**')

      cy.getByCy('beaconNameInput')
        .should('have.value', 'ABC')
        .clear()
        .type('ABCD')

      cy.getByCy('colorPickerButton').click()
      cy.getByCy('colorPickerInput').should('have.value', '#ffffff')
      cy.get('.circle-picker div[title="#4BC27D"]').click()
      cy.getByCy('colorPickerButton').click({ force: true })

      cy.log('**--- Test FAB type is Icon ---**')
        .getByCy('DropList.SelectTagToggler')
        .contains('Icon')
        .log('**--- Test correct FAB icon is selected (the bubble icon) ---**')
        .get('[data-cy=ActionSelectContent] [data-icon-name="fab-bubble"]')
        .parent()
        .find('[data-icon-name="check"]')

      cy.log('**--- Switch FAB icon type from Icon to Text Only ---**')
        .getByCy('DropList.SelectTagToggler')
        .click()
        .getByCy('ActionSelectDropdown')
        .find('li')
        .contains('Text only')
        .click()

      cy.getByCy('displayStyleText').click({ force: true }).clear().type('Wow!')

      cy.get('[data-cy=positionRadioLeft] input').click({ force: true })

      cy.getByCy('zIndexInput')
        .should('have.value', '105')
        .click({ force: true })
        .type('5')

      cy.getByCy('displayShowPoweredBy').should('have.class', 'is-checked')
      cy.getByCy('displayShowPoweredBy').click({ force: true })

      cy.log('**--- Save and assert 200 response ---**')

      cy.getByCy('savePageButton').click()
      cy.waitAndAssertStatusCode('stubBeacon', 200)
    })

    it('Tests the Docs screen can be updated and saved', function () {
      const randomNumber = faker.random.number()

      cy.visit(`/settings/beacons/${beaconId}/docs`)
        .waitAndAssertStatusCode('getPopularArticles', 200)
        .waitAndAssertStatusCode('getAllArticles', 200)

      cy.log('**--- Add a Web Link Custom Suggestion ---**')

      cy.get('.SuggestedArticlesList')
        .find('.c-SortableItem')
        .should('have.length', 5)
        .eq(0)
        .click()
      cy.get('.DropListItem')
        .contains('.DropListItem', 'Add a web link')
        .click({ force: true })

      cy.log('**--- Enter URL and Alias ---**')

      cy.getByCy('customLinkUrlInput').clear().type('https://www.testurl.com')
      cy.getByCy('customLinkTextInput').clear().type(`TESTURL ${randomNumber}`)
      cy.get('button').contains('Insert').click()

      cy.log('**--- Save and assert 200 response ---**')

      cy.getByCy('savePageButton').click()
      cy.waitAndAssertStatusCode('stubBeacon', 200)
    })

    it('Tests the Messaging screen can be updated and saved', function () {
      cy.visit(
        `/settings/beacons/${beaconId}/messaging`
      ).waitAndAssertStatusCode('getCustomFields', 200)

      cy.log('**--- Toggle Chat off ---**')

      cy.get('label[data-cy=messagingChatEnabled]')
        .should('have.class', 'is-checked')
        .find('input')
        .click({ force: true })

      cy.log('**--- Toggle name/subject/attachments off ---**')

      cy.get('#messagingContactFormShowName')
        .should('not.have.class', 'is-checked')
        .click({ force: true })
      cy.get('#messagingContactFormShowSubject')
        .should('not.have.class', 'is-checked')
        .click({ force: true })
      cy.get('#messagingContactFormAllowAttachments')
        .should('not.have.class', 'is-checked')
        .click({ force: true })

      cy.log('**--- Click Advanced Security ---**')

      cy.get('#messagingAuthType')
        .should('not.have.class', 'is-checked')
        .click({ force: true })

      cy.log('**--- Save and assert 200 response ---**')

      cy.getByCy('savePageButton').click({ force: true })
      cy.waitAndAssertStatusCode('stubBeacon', 200)

      cy.log('**--- Toggle Chat on ---**')

      cy.get('label[data-cy=messagingChatEnabled]')
        .find('input')
        .click({ force: true })

      cy.getByCy('savePageButton').click({ force: true })
      cy.waitAndAssertStatusCode('stubBeacon', 200)
    })

    it('Tests the Translate screen can be updated and saved', function () {
      const randomNumber = faker.random.number()
      cy.visit(
        `/settings/beacons/${beaconId}/translate`
      ).waitAndAssertStatusCode('stubBeacon', 200)

      cy.log('**--- Check initial state of inputs, change and save ---**')

      cy.getByCy('AccordionSection').contains('General').click({ force: true })
      cy.get('input#answer')
        .should('have.attr', 'placeholder', 'Answers')
        .clear()
        .type(`ઁૐୁ௲ఊಇഊีຳ ິ ີ ຶ ືླྀტᄵḽἀ‡${randomNumber}`)

      cy.getByCy('AccordionSection').contains('Chat').click({ force: true })
      cy.get('input#chatConnected')
        .should('have.attr', 'placeholder', 'Connected to {{ name }}')
        .clear()
        .type(`ઁૐୁ௲ఊಇഊีຳ ິ ີ ຶ ືླྀტᄵḽἀ‡${randomNumber}`)

      cy.getByCy('savePageButton').click()
      cy.waitAndAssertStatusCode('saveBeaconLabels', 204)
    })
  })
}
