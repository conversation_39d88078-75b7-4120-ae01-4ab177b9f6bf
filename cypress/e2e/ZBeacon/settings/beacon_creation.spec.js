import { hasTags } from '../../../support/utils'
import { DESKTOP_1600x1200 } from '../../../support/constants/browserSizes'
import faker from 'faker'
import { PLUS_COMPANY } from '../../../support/constants/plans'

if (hasTags('smoke', 'beacon-settings', 'beacon-creation')) {
  context('[MESSAGES]: Beacon creation tests', () => {
    const randomNumber = faker.random.number()
    let BeaconE2E

    beforeEach(() => {
      cy.viewport(
        DESKTOP_1600x1200.BROWSER_WIDTH,
        DESKTOP_1600x1200.BROWSER_HEIGHT
      )
      cy.fixture(PLUS_COMPANY.LOGINFILE_EMPIRE).then(data => {
        BeaconE2E = data
        const {
          owner: { email, password },
        } = BeaconE2E.users
        cy.loginByCSRF({ email, password })
      })

      cy.intercept('GET', '/v1/articles/*/popular?ts=*').as('getArticles')
      cy.intercept('POST', '/api/v0/beacons/').as('createBeacon')
      cy.intercept('GET', '/api/v0/beacons/*').as('openBeacon')
      cy.intercept('GET', '/api/v0/reports/beacons?*', {
        sessions: { total: 0 },
        aiResolutions: {},
        perBeaconSummary: {},
      }).as('getReports')
      cy.intercept('DELETE', '/api/v0/beacons/*').as('deleteBeacon')
      cy.intercept('/v1/*/agents', { statusCode: 200 }).as('getAgents')
      cy.intercept('/v1/*/fields/contact-form', { statusCode: 200 }).as(
        'getContact'
      )

      cy.request('settings/beacons/new')
      cy.log('Visit new Beacon screen').visit('settings/beacons/new')
    })

    it('Tests the full creation flow with default docs and mailbox E2E', () => {
      cy.checkPageTitle('Customize').checkDotStepperIsOnStep('1')

      cy.typeBeaconName(randomNumber)

      cy.changeBeaconColor('#000000')

      // This needs refactoring at some point when the ActionSelect component has data-cy selectors
      cy.log('Test FAB type is Icon')
        .getByCy('DropList.SelectTagToggler')
        .contains('Icon')
      cy.log('Test correct FAB icon is selected (the bubble icon)')
        .get('[data-cy=ActionSelectContent] [data-icon-name="fab-bubble"]')
        .parent()
        .find('[data-icon-name="check"]')
      cy.log('Change FAB icon')
        .get('[data-cy=ActionSelectContent] [data-icon-name="fab-hand"]')
        .click({ force: true })
        .parent()
        .find('[data-icon-name="check"]')

      cy.progressToNextStep()
        .checkDotStepperIsOnStep('2')
        .checkPageTitle('Talk with customers')

      cy.progressToNextStep()
        .waitAndAssertStatusCode('getArticles', 200)
        .checkDotStepperIsOnStep('3')
        .checkPageTitle('Self service')

      cy.progressToNextStep()
        .waitAndAssertStatusCode('createBeacon', 200)
        .checkDotStepperIsOnStep('4')
        .checkPageTitle('All done!')

      cy.log('Check code snippet exists and is copiable')
        .getByCy('CopyCode')
        .should('exist')
        .getByCy('CopyButton')
        .click()
        .assertNotyText('Copied to clipboard')

      cy.log('Check Developer Tools section exists')
        .getByCy('BaseIllo')
        .parent()
        .contains('Developer Tools')

      cy.log('Go to Beacon Settings and delete Beacon')
        .getByCy('beaconOnbContinueBtn')
        .click()
        .waitAndAssertStatusCode('openBeacon', 200)
        .url()
        .should('contain', 'customize')
        .deleteBeacon(randomNumber)
    })
  })
}
