import { hasTags } from '../../support/utils'
import {
  PLUS_COMPANY,
  STANDARD_COMPANY,
  PRO_COMPANY,
} from '../../support/constants/plans'

const MONTHLY_ALERT =
  'Your MONTHLY payment of $150 was due on September 25, 2019. '
const SALESTAX_ALERT = 'Sales Tax may apply for US based customers. (Details)'
const ANNUALLY_ALERT = [
  'Your ANNUAL payment of \\$1,440 was due on September 25, 2019\\. ' +
    'Sales Tax may apply for US based customers\\. \\(Details\\)',
  'Your ANNUAL payment of \\$1,584 was due on September 25, 2019\\. ' +
    'Sales Tax may apply for US based customers\\. \\(Details\\)',
]
const ANNUAL_REFUND =
  'Annual plans are not refundable if you decide to cancel after 30 days.'
const ANNUAL_REFUND_DETAILS =
  'Learn more  about our annual payments here or see our Terms of Service.'
const ANNUAL_REFULD_LINK =
  'https://docs.helpscout.com/article/1421-about-annual-payments'
const ANNUAL_REFULD_TOS =
  'https://www.helpscout.com/company/legal/terms-of-service/'

const table = {
  header: {
    plan: 'Plan',
    price: 'Price',
    total: 'Total',
  },
  row: {
    usersMonthly: 'Help Scout Plus (3 Users @ $50/User)',
    usersAnnually: [
      'Help Scout Plus \\(3 Users @ \\$40\\/User\\)',
      'Help Scout Plus \\(3 Users @ \\$44\\/User\\)',
    ],
    docs: 'Docs (0 sites)',
  },
  monthly: {
    usersPrice: '$50',
    userTotal: '$150',
    docsPrice: '$0',
    docsTotal: '$0',
  },
  annual: {
    usersPrice: ['\\$120', '\\$132'],
    userTotal: ['\\$1,440', '\\$1,584'],
    docsPrice: '$0',
    docsTotal: '$0',
  },
  total: {
    totalLabel: 'Total To Pay:',
    toPayMonthly: '$150',
    toPayAnnually: ['\\$1,440', '\\$1,584'],
  },
}
const missingFeatures = {
  users: 'No user limit',
  permissions: 'Advanced permissions',
  teams: 'Teams',
  fields: 'Custom fields',
  premium: 'Salesforce, Jira, HubSpot apps',
  restricted: 'Restricted Docs',
  summarize: 'AI Summarize',
  assist: 'AI Assist',
  reporting: 'Unlimited reporting history',
  api: 'Advanced API access',
  lus: 'Access to light users',
}

const companyRoadblocked = {
  companyId: 2910,
  status: '1', //Company Status: Active
  accountStatus: '2', //Acccount Status: Expired Trial
  subState: '4', //Subscription State: Delinquent
  numProcessed: '0',
  lastPayment: '',
  nextPayment: '2019-09-25',
}

if (hasTags('roadblock')) {
  describe('[GROWTH]: Manage Roadblock page (AO)', () => {
    before(() => {
      cy.log('**--- Precondition: Make sure the Company is roadblocked ---**')
      cy.sumoLoginViaAPI(companyRoadblocked.companyId)
      cy.visit(
        `${Cypress.config().urls.super}/page.php?view=helpscout:company&id=${
          companyRoadblocked.companyId
        }`
      )
      cy.roadblockAccountViaAPI(companyRoadblocked)
    })

    beforeEach(() => {
      cy.session(PLUS_COMPANY.LOGINFILE_ROADBLOCK, () => {
        cy.log('**--- Log in with the Roadblocked account / AO ---**')
        cy.fixture(PLUS_COMPANY.LOGINFILE_ROADBLOCK).then(data => {
          cy.login(data.users.owner)
        })
        cy.location('pathname').should('eq', '/')
      })
      cy.intercept('POST', '/api/v0/members/payment*').as('submitPayment')
      cy.visit('/')
    })

    it('Check Roadblock page: HEADER as an AO', () => {
      cy.log('**--- Check Roadblock page Header ---**')
      cy.getByCy('Roadblock.HeadlineDesc').should(
        'contain',
        `To re-activate your account, choose monthly or annual payment`
      )
      cy.getByCy('Roadblock.HeadlineDesc').should(
        'contain',
        `and enter your details below`
      )

      cy.log('**--- Check Roadblock page body ---**')
      cy.getByCy('BillingCard.title').should(
        'contain',
        `Please choose a billing frequency for Help Scout ${PLUS_COMPANY.NAME}`
      )
      cy.getByCy('BillingCard.description').should(
        'contain',
        'All our plans are backed by a 30-day money-back guarantee'
      )

      cy.log('**--- Check Roadblock page cards ---**')
      cy.get('[data-cy="BillingCard.Monthly"] > label').should(
        'have.class',
        'radio'
      )
      cy.getByCy('BillingCard.Frequency.Monthly').should('contain', 'Monthly')
      cy.getByCy('BillingCard.Price.Monthly').should(
        'contain',
        `$${PLUS_COMPANY.MONTHLY}`
      )
      cy.getByCy('BillingCard.Price.Monthly').should(
        'contain',
        'per user per month'
      )

      cy.get('[data-cy="BillingCard.Annual"] > label').should(
        'have.class',
        'radio'
      )
      cy.getByCy('BillingCard.Frequency.Annual').should('contain', 'Annually')

      cy.getByCy('BillingCard.Price.Annual')
        .invoke('text')
        .should(
          'containOneOf',
          PLUS_COMPANY.ANNUAL.map(price => `\\$${price}`)
        )
      cy.getByCy('BillingCard.Price.Annual').should(
        'contain',
        'per user per month'
      )

      cy.log('**--- Check Roadblock page Footer ---**')
      cy.getByCy('finePrint.CancelAccount').should('contain', 'Delete Account')
      cy.getByCy('finePrint.FAQs').should('contain', 'Plan FAQs')
      cy.getByCy('finePrint.Users').should('contain', 'Manage Users')
    })

    it('Check Roadblock page: PAYMENT as an AO', () => {
      cy.log('**--- Check Roadblock page Payment section (MONTHLY) ---**')
      cy.getByCy('BillingCard.Price.Monthly').click()
      cy.getByCy('PaymentInfo.heading').should('contain', 'Payment Information')
      cy.getByCy('PaymentInfo.description').should($note => {
        expect($note.get(0).innerText).to.include(
          MONTHLY_ALERT + SALESTAX_ALERT
        )
      })

      cy.log('**--- Check Roadblock page Payment section (ANNUALLY) ---**')
      cy.getByCy('BillingCard.Price.Annual').click()
      cy.getByCy('PaymentInfo.heading').should('contain', 'Payment Information')
      cy.getByCy('PaymentInfo.description').should($note => {
        expect($note.get(0).innerText).to.containOneOf(ANNUALLY_ALERT)
      })
    })

    it('Check Roadblock page: DETAILS as an AO', () => {
      cy.log(
        '**--- Check Roadblock page Payment > Details section (MONTHLY) ---**'
      )
      cy.getByCy('BillingCard.Price.Monthly').click()
      cy.getByCy('Roadblock.Details')
        .should('have.text', 'Details')
        .click()
        .should('have.text', 'Hide details')

      cy.log('**--- Check Details table Header (MONTHLY) ---**')
      cy.getByCy('InvoiceBody.Table.Head')
        .find('th')
        .then($ths => {
          expect($ths.eq(0)).to.contain(table.header.plan)
          expect($ths.eq(1)).to.contain(table.header.price)
          expect($ths.eq(2)).to.contain(table.header.total)
        })

      cy.log('**--- Check Details table Body (MONTHLY) ---**')
      cy.getByCy('InvoiceBody.Table.Content')
        .find('td')
        .then($tds => {
          expect($tds.eq(0)).to.contain(table.row.usersMonthly)
          expect($tds.eq(1)).to.contain(table.monthly.usersPrice)
          expect($tds.eq(2)).to.contain(table.monthly.userTotal)
          expect($tds.eq(3)).to.contain(table.row.docs)
          expect($tds.eq(4)).to.contain(table.monthly.docsPrice)
          expect($tds.eq(5)).to.contain(table.monthly.docsTotal)
        })

      cy.log('**--- Check Details table Total (MONTHLY) ---**')
      cy.getByCy('InvoiceBody.Table.Total')
        .find('td')
        .then($ttds => {
          expect($ttds.eq(0)).to.contain(table.total.totalLabel)
          expect($ttds.eq(1)).to.contain(table.total.toPayMonthly)
        })

      cy.log(
        '**--- Check Roadblock page Payment > Details section (ANNUALLY) ---**'
      )
      cy.getByCy('BillingCard.Price.Annual').click()
      cy.log('**--- Check Details table Body (ANNUALLY) ---**')
      cy.getByCy('InvoiceBody.Table.Content')
        .find('td')
        .then($tds => {
          expect($tds.eq(0).text()).to.containOneOf(table.row.usersAnnually)
          expect($tds.eq(1).text()).to.containOneOf(table.annual.usersPrice)
          expect($tds.eq(2).text()).to.containOneOf(table.annual.userTotal)
          expect($tds.eq(3)).to.contain(table.row.docs)
          expect($tds.eq(4)).to.contain(table.annual.docsPrice)
          expect($tds.eq(5)).to.contain(table.annual.docsTotal)
        })

      cy.log('**--- Check Details table Total (ANNUALLY) ---**')
      cy.getByCy('InvoiceBody.Table.Total')
        .find('td')
        .then($ttds => {
          expect($ttds.eq(0)).to.contain(table.total.totalLabel)
          expect($ttds.eq(1).text()).to.containOneOf(table.total.toPayAnnually)
        })
    })

    it('Check Roadblock page: CHANGE PLAN (Monthly) page as an AO', () => {
      cy.getByCy('BillingCard.Price.Monthly').click()
      cy.getByCy('Roadblock.ChangePlan')
        .should('have.text', 'Change Plan')
        .click()
      cy.getByCy('PricingTable.Heading')
        .invoke('text')
        .should(
          'match',
          /Choose your Help Scout plan|Choose a current Help Scout plan/
        )
      cy.getByCy('PricingTable.Subheading').should(
        'have.text',
        'All of them come with a 30-day money back guarantee'
      )
      cy.getByCy('BillingCard.Monthly')
        .should('have.class', 'is-selected')
        .and('contain', 'Monthly')

      checkCardFeatures(PLUS_COMPANY.FIXTURE, 'PlusPlan')
      checkCardFeatures(STANDARD_COMPANY.FIXTURE, 'StandardPlan')
      checkCardFeatures(PRO_COMPANY.FIXTURE, 'ProPlan')
    })

    it('Check Roadblock page: CHANGE PLAN (Annually) page as an AO', () => {
      cy.getByCy('BillingCard.Price.Annual').click()
      cy.getByCy('AnnualPayment.Callout').should('contain', ANNUAL_REFUND)
      cy.getByCy('AnnualPayment.Callout').should(
        'contain',
        ANNUAL_REFUND_DETAILS
      )
      cy.getByCy('Annual.Refund.Link').should(
        'have.attr',
        'href',
        ANNUAL_REFULD_LINK
      )
      cy.getByCy('Annual.Refund.TOS').should(
        'have.attr',
        'href',
        ANNUAL_REFULD_TOS
      )

      cy.getByCy('Roadblock.ChangePlan')
        .should('have.text', 'Change Plan')
        .click()
      cy.getByCy('BillingCard.Annual')
        .should('have.class', 'is-selected')
        .and('contain', 'Annual')

      cy.getByCy('Roadblock.BackToPayment')
        .should('have.text', 'Back to Payment Overview')
        .click()
      cy.getByCy('PaymentInfo.heading').should('be.visible')
    })

    it('Check Roadblock page: CHANGE PLAN (Downgrade) page as an AO', () => {
      // An uncaught exception is thrown on the After each block, after all the tests passed
      cy.on('uncaught:exception', (err, runnable) => {
        expect(err.message).to.include(
          `Cannot read properties of null (reading 'focus'`
        )
        return false
      })
      cy.getByCy('BillingCard.Price.Annual').click()
      cy.getByCy('Roadblock.ChangePlan')
        .should('have.text', 'Change Plan')
        .click()

      cy.log('**--- Attempt to Downgrade to Standard plan ---**')
      cy.getByCy('MemberPlan.Plan.standard').click()
      cy.getByCy('update-plan-modal').should('be.visible')

      cy.getByCy('ModalHeaderV2').should(
        'have.text',
        'Downgrade to Standard plan'
      )
      cy.getByCy('Downgrade.Standard.Modal.Title').should(
        'have.text',
        'The Standard plan does not include'
      )

      cy.getByCy('Downgrade.Standard.Modal.List')
        .find('li')
        .then($tds => {
          expect($tds.eq(0)).to.contain(missingFeatures.users)
          expect($tds.eq(1)).to.contain(missingFeatures.permissions)
          expect($tds.eq(2)).to.contain(missingFeatures.teams)
          expect($tds.eq(3)).to.contain(missingFeatures.fields)
          expect($tds.eq(4)).to.contain(missingFeatures.premium)
          expect($tds.eq(5)).to.contain(missingFeatures.restricted)
          expect($tds.eq(6)).to.contain(missingFeatures.summarize)
          expect($tds.eq(7)).to.contain(missingFeatures.assist)
          expect($tds.eq(8)).to.contain(missingFeatures.reporting)
          expect($tds.eq(9)).to.contain(missingFeatures.api)
          expect($tds.eq(10)).to.contain(missingFeatures.lus)
        })

      cy.getByCy('DowngradePlan.Button').should('have.text', 'Downgrade Plan')
      cy.getByCy('CancelButton')
        .should('have.text', 'Keep my current plan')
        .click()
      cy.getByCy('Roadblock.BackToPayment')
        .should('have.text', 'Back to Payment Overview')
        .click({ force: true })
      cy.getByCy('PaymentInfo.heading').should('contain', 'Payment Information')
    })

    it('Check Roadblock page: FOOTER as an AO', () => {
      cy.getByCy('BillingCard.Price.Annual').click()
      cy.getByCy('finePrint.FAQs').should('have.text', 'Plan FAQs')
      cy.getByCy('finePrint.Users').should('contain', 'Manage Users')
      cy.get('[data-cy="finePrint.Users"] > a').should(
        'have.attr',
        'href',
        '/users/'
      )
    })
  })
}

const checkCardFeatures = (fixture, plan) => {
  cy.fixture(`registration/${fixture}`).then(data => {
    const featuresList = data.roadblockList
    const cardType = `${plan}.List`
    const heading = `${plan}.Heading`
    const price = `${plan}.Price`
    const frequency = `${plan}.Frequency`
    let button, buttonName
    if (plan === 'PlusPlan') {
      button = 'MemberPlan.Plan.plus'
      buttonName = 'Your Current Plan'
    } else if (plan === 'StandardPlan') {
      button = 'MemberPlan.Plan.standard'
      buttonName = 'Select Plan'
    } else if (plan === 'ProPlan') {
      button = 'MemberPlan.Plan.pro'
      buttonName = 'Contact Us'
    }

    cy.getByCy(heading).should('contain', featuresList.plan)
    cy.getByCy(price).should('contain', featuresList.price)
    cy.getByCy(frequency).should('contain', featuresList.billing)
    cy.getByCy(button).should('contain', buttonName)

    cy.getByCy(cardType)
      .children()
      .should($list => {
        expect($list.eq(0)).to.contain(featuresList.previous)
        expect($list.eq(1)).to.contain(featuresList.mailboxLimit)
        expect($list.eq(2)).to.contain(featuresList.docsLimit)
        expect($list.eq(3)).to.contain(featuresList.usersLimit)
        if (featuresList.plan === 'Standard') {
          expect($list.eq(4)).to.contain(featuresList.emailChat)
          expect($list.eq(5)).to.contain(featuresList.views)
          expect($list.eq(6)).to.contain(featuresList.messages)
          expect($list.eq(7)).to.contain(featuresList.socialChannels)
          expect($list.eq(8)).to.contain(featuresList.beacon)
          expect($list.eq(9)).to.contain(featuresList.report)
          expect($list.eq(10)).to.contain(featuresList.workflows)
          expect($list.eq(11)).to.contain(featuresList.properties)
          expect($list.eq(12)).to.contain(featuresList.integrations)
        } else if (featuresList.plan === 'Plus') {
          expect($list.eq(4)).to.contain(featuresList.lightUsersLimit)
          expect($list.eq(5)).to.contain(featuresList.views)
          expect($list.eq(6)).to.contain(featuresList.restrictedDocs)
          expect($list.eq(7)).to.contain(featuresList.customFields)
          expect($list.eq(8)).to.contain(featuresList.teams)
          expect($list.eq(9)).to.contain(featuresList.advPermissions)
          expect($list.eq(10)).to.contain(featuresList.reportingHistory)
          expect($list.eq(11)).to.contain(featuresList.apiAccess)
          expect($list.eq(12)).to.contain(featuresList.premiumApps)
          expect($list.eq(13)).to.contain(featuresList.aiSummarize)
          expect($list.eq(14)).to.contain(featuresList.aiAssist)
          expect($list.eq(15)).to.contain(featuresList.extraLUs)
        } else if (featuresList.plan === 'Pro') {
          expect($list.eq(4)).to.contain(featuresList.lightUsersLimit)
          expect($list.eq(5)).to.contain(featuresList.views)
          expect($list.eq(6)).to.contain(featuresList.discounts)
          expect($list.eq(7)).to.contain(featuresList.security)
          expect($list.eq(8)).to.contain(featuresList.HIPAA)
          expect($list.eq(9)).to.contain(featuresList.APIrateLimit)
          expect($list.eq(10)).to.contain(featuresList.concierge)
          expect($list.eq(11)).to.contain(featuresList.accountManager)
          expect($list.eq(12)).to.contain(featuresList.reviews)
          expect($list.eq(13)).to.contain(featuresList.training)
          expect($list.eq(14)).to.contain(featuresList.extraLUs)
        }
      })
  })
}
