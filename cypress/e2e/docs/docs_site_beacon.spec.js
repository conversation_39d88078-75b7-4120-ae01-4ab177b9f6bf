import { hasTags } from '../../support/utils'
import { PLUS_COMPANY } from '../../support/constants/plans'
import { DESKTOP_1600x1600 } from '../../support/constants/browserSizes'

let newSiteID, mailboxName, password, authToken
const SITE_BEACON = PLUS_COMPANY.LOGINFILE_E2EDOCS

if (hasTags('docs', 'site-settings', 'hs-app-ui', 'docs-api')) {
  describe('[DOCS]: Check the Doc Site Settings page', () => {
    before(() => {
      cy.log('**--- Log in with the SITE_BEACON account ---**')
      cy.fixture(SITE_BEACON).then(data => {
        cy.loginByCSRF(data.users.owner)
      })

      cy.log('Create Docs site')
      cy.fixture(SITE_BEACON).then(data => {
        mailboxName = data.mailboxes.support.name
        password = data.users.owner.password
        authToken = data.authToken
        cy.createNewSiteViaAPI('new-site', authToken).then(id => {
          newSiteID = id
        })
      })
    })

    beforeEach(() => {
      cy.session(SITE_BEACON, () => {
        cy.log('**--- Log in with the SITE_BEACON account ---**')
        cy.fixture(SITE_BEACON).then(data => {
          cy.loginByCSRF(data.users.owner)
        })
      })
      cy.viewport(
        DESKTOP_1600x1600.BROWSER_WIDTH,
        DESKTOP_1600x1600.BROWSER_HEIGHT
      )
      cy.intercept(
        'PUT',
        `${Cypress.config().urls.docsApi}/v1/sites/${newSiteID}/beacon`
      ).as('enableSiteBeacon')
      cy.intercept(
        'GET',
        `${Cypress.config().urls.docsApi}/v1/sites/${newSiteID}/beacon`
      ).as('getSiteBeacon')
    })

    it('Check the Beacon link is shown in the left sidebar', () => {
      cy.visit(`/settings/docs/site/${newSiteID}`)
      cy.log('**--- The Beacon link is shown in the left sidebar---**')
      cy.getByCy('Sidebar.docsBeacon').should('be.visible')
    })

    it('Check Enable Button is not shown yet', () => {
      cy.visit(`/settings/docs/beacon/${newSiteID}`)
      cy.url().should('include', '/settings/docs/beacon')

      cy.getByCy('Docs.Site.Alert').should(
        'contain',
        'Add a collection first to enable Beacon'
      )
      cy.getByCy('Docs.Site.AddCollection').should(
        'contain',
        'Add a collection'
      )
      cy.getByCy('Docs.Beacon.Enabled').should('not.exist')

      cy.log('*--- Adds collection to site ---*')
      cy.getByCy('Docs.Site.AddCollection').click()
      cy.url().should('include', '/settings/docs/collections')
      cy.getByCy('Docs.Collections.newCollection')
        .should('contain', 'New Collection')
        .click()
      cy.getByCy('Docs.Collections.Name').type('Test Collection')
      cy.getByCy('Docs.Collections.Create')
        .should('contain', 'Create Collection')
        .click()
      cy.url().should('include', '/collection/')
    })

    it('Runs through Docs site Beacon creation E2E', () => {
      cy.log('Check the Inbox selection is not yet shown and Enable the Beacon')
      cy.visit(`/settings/docs/beacon/${newSiteID}`)
      cy.url().should('include', '/settings/docs/beacon')

      cy.getByCy('Docs.Site.Alert').should('not.exist')
      cy.getByCy('Docs.Site.AddCollection').should('not.exist')

      cy.log('**--- Beacon toggle is OFF by default ---**')
      cy.getByCy('Docs.Beacon.Enabled').should('not.have.class', 'active')
      cy.log('**--- The inbox selection is not shown yet ---**')
      cy.getByCy('Docs.Beacon.beaconMailboxId.Label').should('not.be.visible')

      cy.log('**--- Enables the Beacon ---**')
      cy.get('.docs-options').should('have.css', 'display', 'none')
      cy.getByCy('Docs.Beacon.Enabled')
        .should('not.have.class', 'active')
        .click({ force: true })
        .should('have.class', 'active')
      cy.get('.docs-options').should('have.css', 'display', 'block')

      cy.log('Select Inbox for the Beacon')
      cy.log('**--- The Inbox selection is shown ---**')
      cy.getByCy('Docs.Beacon.beaconMailboxId.Label').should(
        'have.text',
        'Connected Inbox'
      )

      cy.log('**--- Select an inbox ---**')
      cy.getByCy('Docs.Beacon.beaconMailboxId')
        .find('option:selected')
        .should('have.text', mailboxName)
        .should('not.be.disabled')

      cy.getByCy('Docs.Beacon.beaconMailboxId').select(mailboxName)

      cy.log('**--- Associate an Inbox to the Beacon site ---**')
      cy.getByCy('Docs.Beacon.Save')
        .click()
        .assertNotyText('Beacon enabled')
        .waitAndAssertStatusCode('enableSiteBeacon', 200)
        .waitAndAssertStatusCode('getSiteBeacon', 200)

      cy.log('**--- Once saved, the Inbox selection is NOT shown anymore ---**')
      cy.getByCy('Docs.Beacon.beaconMailboxId.Label').should('not.be.visible')

      cy.log('Visit the Site beacon settings')
      cy.log('**--- Customization is allowed now ---**')
        .get('.customizeBeacon > a', { timeout: 10000 })
        .should('contain', 'Customize Beacon')
        .get('[name="customizeBeacon"]')
        .click({ force: true })
      cy.url().should('include', '/customize')

      cy.log('**--- A Docs Site Beacon alert is shown ---**')
      cy.getByCy('SettingsCustomizeDocsSiteInfo')
        .should('exist')
        .and('contain', 'This Beacon is used by ')

      cy.log('**--- A Delete Beacon button is not shown---**')
      cy.getByCy('deleteBeaconButton').should('not.exist')

      cy.log('**--- Contact / Messaging---**')
      cy.getByCy('Beacon.Layout.Contact').should('contain', 'Contact').click()
      cy.url().should('include', '/messaging')

      cy.log('**--- A Docs Site Beacon alert is shown ---**')
      cy.getByCy('SettingsCustomizeDocsSiteInfo')
        .should('exist')
        .and('contain', 'This Beacon is used by ')

      cy.log('**--- Inbox selected is shown ---**')
      cy.getByCy('messagingMailboxId').should('contain', mailboxName)

      cy.log('**--- Contact Form toggle is enabled by default ---**')
      cy.getByCy('contactFormEnabled').should('have.class', 'is-checked')

      cy.log('**--- Chat toggle is disabled by default ---**')
      cy.getByCy('messagingChatEnabled').should('not.have.class', 'is-checked')

      cy.getByCy('Beacon.Layout.Translate')
        .should('contain', 'Translate')
        .click()

      cy.log('**--- Chat toggle is disabled by default ---**')
      cy.getByCy('language').should('contain', 'English')

      cy.log('Verify the left sidebar links')
      cy.log('**--- No link for Docs ---**')
      cy.getByCy('Beacon.Layout.Docs').should('not.exist')

      cy.log('**--- No link for Installation ---**')
      cy.getByCy('Beacon.Layout.Installation').should('not.exist')

      cy.log('**--- No link for All Beacons ---**')
      cy.getByCy('Beacon.Layout.AllBeacons').should('not.exist')

      cy.log('Navigate back to the Beacon page and disable it')
      cy.getByCy('Beacon.Layout.Customize')
        .should('contain', 'Customize')
        .click()
      cy.url().should('include', '/customize')

      cy.getByCy('SettingsCustomizeDocsSiteLink', { timeout: 10000 })
        .click()
        .url()
        .should('include', '/settings/docs/site')
      cy.getByCy('Sidebar.docsBeacon').click()

      cy.log('**--- Disables the Beacon ---**')
      cy.getByCy('Docs.Beacon.Enabled').should('have.class', 'active').click()
      cy.getByCy('Docs.Beacon.Enabled').should('not.have.class', 'active')

      cy.log('Delete existing Docs Site')
      cy.deleteSiteViaAPI(newSiteID, password)
    })
  })
}
