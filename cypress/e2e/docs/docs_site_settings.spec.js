import { hasTags } from '../../support/utils'
import { STANDARD_COMPANY } from '../../support/constants/plans'
import { DESKTOP_1600x1200 } from '../../support/constants/browserSizes'
import { getDocsDefault2ndLevelDomain } from '../../support/ciUtils'
import faker from 'faker'

let companyId = ''
let userId = ''
let siteName = ''
let siteSlug = ''
let mailboxData
let password = ''
const rn1 = faker.random.number()
let siteNameUpdated = ''
let siteSlugUpdated = ''
let docsDefault2ndLevelDomain

const brandingColors = {
  defaultColors: {
    articleTextColor: '#585858',
    headerBgColor: '#2c323d',
    linkColor: '#4381b5',
    navActiveColor: '#ffffff',
    navTextColor: '#c0c0c0',
    pageBgColor: '#ffffff',
  },
  changedColor: {
    articleTextColor: '#0035ff',
    headerBgColor: '#3669c7',
    linkColor: '#3fa134',
    navActiveColor: '#f7f7d7',
    navTextColor: '#9d15ec',
    pageBgColor: '#00f9ee',
  },
}

if (
  hasTags('docs', 'site-settings', 'specific-plan', 'hs-app-ui', 'docs-api')
) {
  describe(
    '[DOCS]: Check the Doc Site Settings page',
    { testIsolation: false },
    () => {
      before(() => {
        cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
          companyId = data.id
          mailboxData = data.mailboxes
          userId = data.users.owner.id
          password = data.users.owner.password
          docsDefault2ndLevelDomain = getDocsDefault2ndLevelDomain()
        })

        const slugPrefix = 'cytest-'
        cy.createIdForEnv({ offset: slugPrefix.length }).then(id => {
          siteName = `CyTest ${id}${rn1}`
          siteSlug = `${slugPrefix}${id}${rn1}`
          siteNameUpdated = `UPDATED ${id}${rn1}`
          siteSlugUpdated = `updated-${id}${rn1}`
        })
      })

      after(() => {
        cy.reloadSiteDataViaAPI(companyId, userId)
      })

      beforeEach(() => {
        cy.session(STANDARD_COMPANY, () => {
          cy.log('**--- Log in with the Standard account ---**')
          cy.fixture(STANDARD_COMPANY.LOGINFILE).then(data => {
            cy.loginByCSRF(data.users.owner)
          })
        })
        cy.viewport(
          DESKTOP_1600x1200.BROWSER_WIDTH,
          DESKTOP_1600x1200.BROWSER_HEIGHT
        )
        cy.intercept('GET', '/v1/collections?siteId=*&page=*').as(
          'loadDocsHomeCollections'
        )
        cy.intercept('POST', '/v1/sites?reload=true').as('createSite')
        cy.intercept('DELETE', '/docs/site/*').as('deleteDocsSite')
        cy.intercept('PUT', 'v1/sites/*?reload=true').as('saveSettings')
        cy.intercept('POST', 'v1/collections?reload=true').as('saveCollections')
      })

      it('Create an additional Site that will be charged $20', () => {
        const alert = 'Additional Docs sites cost $20/site/month. '
        const alert2 = 'The charge will be added to your next invoice.'
        cy.visit('/settings/docs')
        cy.url().should('include', '/settings/docs')

        cy.log('**--- Create an additional new site ---**')
        cy.getByCy('Docs.New.Site').click()
        cy.getByCy('Docs.NewSite.Note').should($note => {
          expect($note.get(0).innerText).to.include(alert + alert2)
        })

        cy.getByCy('Docs.NewSite.Name')
          .should('have.attr', 'placeholder', 'Site Name')
          .type(siteName, { delay: 0 })

        cy.getByCy('Docs.NewSite.Subdomain').should('have.value', siteSlug)

        cy.getByCy('Docs.NewSite.Create')
          .should('contain', 'Create Site')
          .click()
          .waitAndAssertStatusCode('createSite', 200)
          .waitAndAssertStatusCode('loadDocsHomeCollections', 200)

        cy.url()
          .should('include', '/settings/docs/site')
          .getByCy('Docs.Site.Name')
          .should('have.value', siteName)
      })
      it('Check Site settings Header', () => {
        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Check Header ---**')
        cy.getByCy('Docs.Site.Heading').should('contain', 'Site Settings')
        cy.getByCy('Docs.Site.Alert').should(
          'contain',
          `This site is not available yet because it doesn't have any collections.`
        )
        cy.getByCy('Docs.Site.AddCollection')
          .should('contain', 'Add a collection')
          .and('have.attr', 'href')
          .and('include', '/settings/docs/collections/')
      })

      it('Check left sidebar menu options', () => {
        cy.getByCy('Sidebar.docsSiteSettings').should('contain', 'Customize')
        cy.getByCy('Sidebar.docsCustomCode').should('contain', 'Custom Code')
        cy.getByCy('Sidebar.docsBeacon').should('contain', 'Beacon')
        cy.getByCy('Sidebar.docsCollections').should('contain', 'Collections')
        cy.getByCy('Sidebar.docsRedirects').should('contain', 'Redirects')
        cy.getByCy('Sidebar.docsTranslate').should('contain', 'Translate')
      })

      it('Check Site settings Basics section', () => {
        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Check Basics ---**')
        cy.get('legend').contains('Basics')
        cy.getByCy('Docs.Site.Title').should('contain', 'Site Name')
        cy.getByCy('Docs.Site.Name')
          .should('have.value', siteName)
          .and(
            'have.attr',
            'data-content',
            `The title will display on the top left of the website if you don't have a logo.`
          )

        cy.getByCy('Docs.Site.Visibility.Label').should(
          'contain',
          'Site Visibility'
        )

        cy.getByCy('Docs.Site.Visibility').should('have.class', 'active')
        cy.getByCy('Docs.Site.VisitSite').should(
          'have.attr',
          'href',
          `https://${siteSlug}.${docsDefault2ndLevelDomain}?auth=true`
        )
        cy.getByCy('Docs.Site.Visibility.Info').should(
          'contain',
          'Turns your site on or off to visitors.'
        )

        cy.getByCy('Docs.Site.subDomain.Label').should('contain', 'Sub-domain')
        cy.getByCy('Docs.Site.SubDomain').should('have.value', siteSlug)
        cy.getByCy('Docs.Site.SubDomain.Prefix').should(
          'contain',
          docsDefault2ndLevelDomain
        )

        cy.log('**--- Check Custom Name ---**')
        cy.getByCy('Docs.Site.CName.Label').should('contain', 'Custom Domain')
        cy.getByCy('Docs.Site.CName').should(
          'have.attr',
          'placeholder',
          'optional'
        )
        cy.getByCy('Docs.Site.CName.Info').should($note => {
          expect($note.get(0).innerText).to.include(
            `Follow these instructions to point a custom domain to Help Scout.\nDomains and SSL certificates can take up to 24 hours to take effect`
          )
        })
      })

      it('Check Site settings Branding section', () => {
        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Check Branding ---**')
        cy.get('legend').contains('Branding')
        cy.getByCy('Docs.Site.Logo.Label').should('contain', 'Logo')
        cy.getByCy('Docs.Site.Logo').should('be.empty')
        cy.getByCy('Docs.Site.Logo.Info').should(
          'contain',
          'Will be re-sized to 75px height. JPG, GIF and PNG are accepted'
        )

        cy.getByCy('Docs.Site.Favicon.Label').should('contain', 'Favicon')
        cy.getByCy('Docs.Site.Favicon.favPreview')
        cy.get('#favPreview')
          .should('have.attr', 'src')
          .and('contain', 'assets/ico/favicon.ico')
        cy.getByCy('Docs.Site.Favicon').should('be.empty')
        cy.getByCy('Docs.Site.Favicon.Info').should($note => {
          expect($note.get(0).innerText).to.include(
            `This icon is used in the browser to identify your website.\n32x32 ICO and PNG file types are accepted.`
          )
        })

        cy.getByCy('Docs.Site.TouchIcon.Label').should('contain', 'Touch icon')
        cy.getByCy('Docs.Site.TouchIcon.Image')
          .should('have.attr', 'src')
          .and('contain', 'assets/ico/touch-152.png')
        cy.getByCy('Docs.Site.TouchIcon').should('be.empty')
        cy.getByCy('Docs.Site.TouchIcon.Info').should($note => {
          expect($note.get(0).innerText).to.include(
            `This icon is used if your site is saved to the home screen of an iOS device.\nWill be re-sized to 152px x 152px, JPG, GIF and PNG are accepted.`
          )
        })
        cy.log('**--- Check Default colors are shown ---**')
        checkBrandingColors()

        cy.log('**--- Open the Reset colors to defaults modal ---**')
        cy.getByCy('Docs.Site.resetColors').should(
          'contain',
          'Reset colors to defaults'
        )
        cy.get('[data-cy="Docs.Site.resetColors"] > a').click()
        cy.getByCy('noty.modal')
          .should('be.visible')
          .getByCy('notyMessage')
          .should(
            'contain',
            'Reset the branding colors to their default Help Scout values?'
          )
        cy.log('**--- Cancel action and do not reset colors to default ---**')
        cy.get('[data-cy="noty.modal"] .noty_buttons')
          .contains('Cancel')
          .click()
        cy.log('**--- Check Default colors are STILL shown ---**')
        checkBrandingColors()
      })

      it('Check Site settings Site Information section', () => {
        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Check Site Information ---**')
        cy.get('legend').contains('Site Information')
        cy.getByCy('Docs.Site.homeUrl.Label').should('contain', 'Home Page URL')
        cy.getByCy('Docs.Site.homeUrl')
          .should('have.attr', 'placeholder', 'optional')
          .and(
            'have.attr',
            'data-content',
            'Visitors will need an easy way to get back to your main company site, which is what this is used for.'
          )

        cy.getByCy('Docs.Site.LogoLink.Text').should(
          'contain',
          'Link the site logo to my home page'
        )

        cy.getByCy('Docs.Site.Description.Label').should(
          'contain',
          'Site Description'
        )
        cy.getByCy('Docs.Site.Description')
          .should('have.attr', 'placeholder', 'optional')
          .and(
            'have.attr',
            'data-content',
            'This text will be shown on the home page above the search bar and is limited to 50 characters.'
          )

        cy.getByCy('Docs.Site.metaDescription.Label').should(
          'contain',
          'Meta Description'
        )
        cy.getByCy('Docs.Site.metaDescription').should(
          'have.attr',
          'data-content',
          'The meta description is used by search engines to describe the website, and is limited to 160 characters.'
        )

        cy.getByCy('Docs.Site.homePageContent.Label').should(
          'contain',
          'Show on Home Page'
        )
        cy.getByCy('Docs.Site.homePageContent').and(
          'have.attr',
          'data-content',
          `This setting determines which content to display on your Docs site home page under each collection: <a href='https://docs.helpscout.com/article/723-homepage-layout-options' target='_blank'>Most Popular Articles or Categories</a>.`
        )
        cy.get('[data-cy="Docs.Site.homePageContent"] option:selected').should(
          'have.text',
          'Most Popular Articles'
        )
      })

      it('Should update information on Site Settings sections', () => {
        const docDetails = {
          photo: faker.random.arrayElement([
            'users/chloe-buddy.png',
            'users/snowball.png',
          ]),
        }

        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Update info on Basics section ---**')
        cy.getByCy('Docs.Site.Name').clear().type(siteNameUpdated, { delay: 0 })
        cy.getByCy('Docs.Site.SubDomain')
          .clear()
          .type(siteSlugUpdated, { delay: 0 })
        cy.getByCy('Docs.Site.CName')
          .clear()
          .type(`docs.mycustomdomain${rn1}.net`, { delay: 0 })

        cy.log('**--- Update images on Branding section ---**')
        cy.uploadImage(
          '[data-cy="Docs.Site.Logo"]',
          docDetails.photo,
          'image/png'
        )
        cy.uploadImage(
          '[data-cy="Docs.Site.Favicon"]',
          docDetails.photo,
          'image/png'
        )
        cy.get('#favPreview')
          .should('have.attr', 'src')
          .and('contain', 'assets/ico/favicon.ico')
        cy.uploadImage(
          '[data-cy="Docs.Site.TouchIcon"]',
          docDetails.photo,
          'image/png'
        )
        cy.getByCy('Docs.Site.TouchIcon.Image')
          .should('have.attr', 'src')
          .and('contain', 'assets/ico/touch-152.png')

        cy.log('**--- Update colors on Branding section ---**')
        changeBrandingColors()

        cy.log('**--- Update info on Site Information section ---**')
        cy.getByCy('Docs.Site.homeUrl')
          .clear()
          .type('https://myhomepageurl.net', { delay: 0 })
        cy.getByCy('Docs.Site.LogoLink').check()
        cy.getByCy('Docs.Site.homeLinkText')
          .should(
            'have.attr',
            'data-content',
            'A link to your Docs home page will be added to the navigation since the logo links to your main site. You can customize what it says here. Something like "Support Home" would work well for most cases.'
          )
          .clear()
          .type('<EMAIL>', { delay: 0 })
        cy.getByCy('Docs.Site.Description').type(
          'Text shown above the Search Bar',
          { delay: 0 }
        )
        cy.getByCy('Docs.Site.metaDescription').type(
          `You need the dark in order to show the light. Only think about one thing at a time. Don't get greedy. You can create beautiful things - but you have to see them in your mind first. I really believe that if you practice enough you could paint the 'Mona Lisa' with a two-inch brush.`,
          { delay: 0 }
        )
        cy.getByCy('Docs.Site.homePageContent').select('Categories')

        cy.log('**--- Update info on Feedback section ---**')
        cy.getByCy('Docs.Site.ArticleRatings').click()

        cy.log('**--- Save settings ---**')
        cy.getByCy('Docs.Site.Save')
          .should('contain', 'Save')
          .click()
          .waitAndAssertStatusCode('saveSettings', 200)
        cy.assertNotyText('Settings saved successfully')
      })

      it('Reset Branding colors to default', () => {
        cy.log('**--- Check Page Background Color was changed---**')
        cy.getByCy('Docs.Site.pageBgColor').should(
          'have.value',
          brandingColors.changedColor.pageBgColor
        )
        cy.log('**--- Reset colors to default ---**')
        cy.getByCy('Docs.Site.resetColors').should(
          'contain',
          'Reset colors to defaults'
        )
        cy.get('[data-cy="Docs.Site.resetColors"] > a').click()
        cy.url().should('include', '#modal')
        cy.getByCy('noty.modal').should('be.visible')
        cy.get('[data-cy="noty.modal"] .noty_text').contains(
          'Reset the branding colors to their default Help Scout values?'
        )
        cy.get('[data-cy="noty.modal"] .noty_buttons').contains('OK').click()
        checkBrandingColors()
      })

      it('Deny user access to Custom Code screen when site is not web accessible', () => {
        cy.log('**--- Create collection so site will appear on dashboard ---**')
        cy.getByCy('Sidebar.docsCollections').click()
        cy.url().should('include', '/settings/docs/collections')

        cy.getByCy('Docs.Collections.newCollection').click()
        cy.getByCy('Docs.Collections.Name').type('Article Collection', {
          delay: 0,
        })
        cy.getByCy('Docs.Collections.Create')
          .click({ force: true })
          .waitAndAssertStatusCode('saveCollections', 201)
          .get('.accordion-group')
          .should('have.length', 1)

        cy.log('**--- Verify public site and custom code option ---**')
        cy.getByCy('Sidebar.docsSiteSettings').click()
        cy.url().should('include', '/settings/docs/site')

        cy.getByCy('Docs.Site.Visibility').should('have.class', 'active')
        cy.getByCy('Sidebar.docsCustomCode').should('be.visible')

        // Spy on reset-collections request.
        cy.intercept({
          method: 'GET',
          url: '/docs/reset-collections',
        }).as('collections')

        cy.log('**--- Make site private ---*')
        cy.getByCy('Docs.Site.Visibility').click()
        cy.getByCy('Docs.Site.Save').click()
        cy.assertNotyText('Settings saved successfully')

        // As part of save, wait until reset-collections request completes.
        cy.waitAndAssertStatusCode('collections', 200)

        cy.log('**--- Verify private site and no custom code option ---**')
        cy.getByCy('Docs.Site.Visibility').should('not.have.class', 'active')
        cy.getByCy('Sidebar.docsCustomCode').should('not.be.visible')

        cy.log('**--- Visit dashboard ---**')
        cy.visit('/')

        cy.log('**--- Verify Custom Code option hidden ---**')
        cy.get('.card.docs')
          .last()
          .within(() => {
            cy.get('.dropdown-toggle').click()
            cy.get('.dropdown-menu')
              .contains('Site Settings')
              .should('be.visible')
            cy.get('.dropdown-menu').contains('Custom Code').should('not.exist')

            cy.log('**--- Verify cannot access custom code page ---**')
            cy.get('.dropdown-menu')
              .contains('Site Settings')
              .should('have.attr', 'href')
              .then(href => {
                cy.visit(href.replace('/site/', '/custom-code/'))
              })
          })

        cy.log('**--- Verify redirected to settings ---**')
        cy.assertNotyText('Custom Code is not available on private sites')
        cy.url().should('include', '/settings/docs/site')
      })

      it('Allow user access to Custom Code screen when site is public', () => {
        cy.url().should('include', '/settings/docs/site')

        cy.log('**--- Verify private site and no custom code option ---**')
        cy.getByCy('Docs.Site.Visibility').should('not.have.class', 'active')
        cy.getByCy('Sidebar.docsCustomCode').should('not.be.visible')

        // Spy on reset-collections request.
        cy.intercept({
          method: 'GET',
          url: '/docs/reset-collections',
        }).as('collections')

        cy.log('**--- Make site public ---**')
        cy.getByCy('Docs.Site.Visibility').click()
        cy.getByCy('Docs.Site.Save').click()
        cy.assertNotyText('Settings saved successfully')

        // As part of save, wait until reset-collections request completes.
        cy.waitAndAssertStatusCode('collections', 200)

        cy.log('**--- Verify public site and custom code option ---**')
        cy.getByCy('Docs.Site.Visibility').should('have.class', 'active')
        cy.getByCy('Sidebar.docsCustomCode').should('be.visible')

        cy.log('**--- Verify can access custom code page ---**')
        cy.getByCy('Sidebar.docsCustomCode').click()
        cy.url().should('include', '/settings/docs/custom-code')

        cy.log('**--- Visit dashboard ---*')
        cy.visit('/').reload()

        cy.log('**--- Verify Custom Code option visible ---**')
        cy.get('.card.docs')
          .last()
          .within(() => {
            cy.get('.dropdown-toggle').click()
            cy.get('.dropdown-menu')
              .contains('Custom Code')
              .should('be.visible')

            cy.log('**--- Verify can access custom code page via option ---**')
            cy.get('.dropdown-menu').contains('Custom Code').click()
            cy.url().should('include', '/settings/docs/custom-code')
          })

        cy.log('**--- Return to settings page ---**')
        cy.getByCy('Sidebar.docsSiteSettings').click()
        cy.url().should('include', '/settings/docs/site')
      })

      it('Should delete a site', () => {
        cy.log('**--- Delete an existing site ---**')
        cy.visit('/settings/docs')
        cy.getByCy('Docs.Sites.Heading').should('contain', 'Docs Sites')
        cy.getByCy('Docs.CardList').contains(siteNameUpdated).click()

        cy.getByCy('Docs.Site.Name').should('have.value', siteNameUpdated)
        cy.getByCy('Docs.Site.Delete').click()

        cy.getByCy('ModalHeaderV2').should(
          'contain',
          `Delete '${siteNameUpdated}' Docs Site`
        )
        cy.getByCy('ModalBody').within(() => {
          cy.contains(
            'All collections, categories, and articles in this docs site will be deleted and cannot be undone. Are you sure you want to continue?'
          )
          cy.get('label').should('contain', 'Enter your password to confirm')
          cy.get('input').should('have.attr', 'type', 'password')
          cy.get('a').should('have.attr', 'href', '/members/forgot-password')

          cy.getByCy('ConfirmPasswordOrWordModal.InputPassword').type(
            password,
            {
              force: true,
              delay: 0,
            }
          )
        })
        cy.getByCy('ConfirmPasswordOrWordModal.Footer').within(() => {
          cy.getByCy('PrimaryButton')
            .contains('Delete Docs Site')
            .click()
            .waitAndAssertStatusCode('deleteDocsSite', 204)
        })

        cy.url().should('include', `/settings/docs/`)

        cy.getByCy('Docs.CardList')
          .contains(siteNameUpdated)
          .should('not.exist')
      })
    }
  )
}

const checkBrandingColors = () => {
  cy.getByCy('Docs.Site.pageBgColor.Label').should(
    'contain',
    'Page Background Color'
  )
  cy.getByCy('Docs.Site.pageBgColor').should(
    'have.value',
    brandingColors.defaultColors.pageBgColor
  )
  cy.getByCy('Docs.Site.headerBgColor.Label').should(
    'contain',
    'Header Background Color'
  )
  cy.getByCy('Docs.Site.headerBgColor').should(
    'have.value',
    brandingColors.defaultColors.headerBgColor
  )
  cy.getByCy('Docs.Site.navTextColor.Label').should('contain', 'Nav Text Color')
  cy.getByCy('Docs.Site.navTextColor').should(
    'have.value',
    brandingColors.defaultColors.navTextColor
  )
  cy.getByCy('Docs.Site.navActiveColor.Label').should(
    'contain',
    'Nav Active Color'
  )
  cy.getByCy('Docs.Site.navActiveColor').should(
    'have.value',
    brandingColors.defaultColors.navActiveColor
  )
  cy.getByCy('Docs.Site.linkColor.Label').should('contain', 'Link Color')
  cy.getByCy('Docs.Site.linkColor').should(
    'have.value',
    brandingColors.defaultColors.linkColor
  )
  cy.getByCy('Docs.Site.articleTextColor.Label').should(
    'contain',
    'Article Text Color'
  )
  cy.getByCy('Docs.Site.articleTextColor').should(
    'have.value',
    brandingColors.defaultColors.articleTextColor
  )
}

const changeBrandingColors = () => {
  pickColor(
    '[data-cy="Docs.Site.pageBgColor.Control"] .sp-replacer',
    brandingColors.changedColor.pageBgColor
  )
  pickColor(
    '[data-cy="Docs.Site.headerBgColor.Control"] .sp-replacer',
    brandingColors.changedColor.headerBgColor
  )
  pickColor(
    '[data-cy="Docs.Site.navTextColor.Control"] .sp-replacer',
    brandingColors.changedColor.navTextColor
  )
  pickColor(
    '[data-cy="Docs.Site.navActiveColor.Control"] .sp-replacer',
    brandingColors.changedColor.navActiveColor
  )
  pickColor(
    '[data-cy="Docs.Site.linkColor.Control"] .sp-replacer',
    brandingColors.changedColor.linkColor
  )
  pickColor(
    '[data-cy="Docs.Site.articleTextColor.Control"] .sp-replacer',
    brandingColors.changedColor.articleTextColor
  )
}

const pickColor = (element, changedColor) => {
  cy.get(element).click()
  cy.get('.sp-container')
    .not('.sp-hidden')
    .then(input => {
      cy.wrap(input).find('input').clear().type(changedColor, { delay: 0 })
    })
  cy.get(element).click()
  cy.get('.sp-container').should('not.be.visible')
}
