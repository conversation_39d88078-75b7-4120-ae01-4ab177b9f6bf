import faker from 'faker'
let company
const generateNewUser = (riskLevel, plantype, role = '1') => {
  const domain = riskLevel === 'risky' ? 'spamlottery.tv' : 'example.net'
  const fname = faker.name.firstName()
  const lname = faker.name.lastName()
  const randomNumber = faker.random.number()
  const email = `${fname}.${lname}@${domain}`
  //Commenting out line that sets a variable password for debugging purposes
  //Make sure it's long enough
  //const password = faker.internet.userName() + faker.random.number() + '1234'
  const password = 'happy11234567'
  //role=3=User, role=2=Administrator, role=1=Account Owner

  //Concatenated plantype to Company's name to clearly visualize them when listed
  //or when accidentaly added to web dev parity environment (WDP)
  //const company = 'Cy_' + `${plantype}${branchName}` +' - '+ faker.company.companyName()
  cy.createIdForEnv().then(branchName => {
    company = `Cy-${plantype}-${fname}${randomNumber}-${branchName}`
    //Leaving this log for debugging purposes when tests fail and we want to
    //track down which account was used in that test
    cy.log(company)
    const user = {
      fname,
      lname,
      company,
      email,
      password,
      domain,
      role,
    }
    return cy.wrap(user)
  })
}

Cypress.Commands.add('generateNewUser', generateNewUser)
