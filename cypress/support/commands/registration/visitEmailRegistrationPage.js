Cypress.Commands.add('visitEmailRegistrationPage', planUrl => {
  cy.intercept('GET', `https://accounts.google.com/gsi/*`, { statusCode: 200 })
  cy.intercept('POST', '/api/v0/save-registration-data').as(
    'saveRegistrationData'
  )

  cy.visit(planUrl)
  cy.url().should('include', planUrl)
  cy.waitAndAssertStatusCode('saveRegistrationData', 200)
  cy.get('input[name="password"]').should('be.visible')
})
