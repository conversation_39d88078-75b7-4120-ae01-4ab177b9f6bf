const completeSegmentation = ({
  company,
  currentSolution = 'Email',
  currentHelpDesk,
  companyEmployeeCount = '11-50',
  companyIndustry = 'Software',
} = {}) => {
  cy.log('Complete Segmentation Questions screen')
  cy.get('#segmentationNextStep').should('be.visible').and('be.disabled')
  cy.getByCy('Input.companyName').click().type(company)
  cy.getByCy('DropList.currentSolution')
    .click()
    .getByCy('Dropdown.select.currentSolution')
    .eq(0)
    .contains('li', currentSolution)
    .click()
  // eslint-disable-next-line chai-friendly/no-unused-expressions
  currentHelpDesk &&
  cy.getByCy('DropList.currentHelpDesk')
    .click()
    .getByCy('Dropdown.select.currentHelpDesk')
    .eq(0)
    .contains('li', currentHelpDesk)
    .click()
  cy.getByCy('DropList.companyEmployeeCount')
    .click()
    .getByCy('Dropdown.select.companyEmployeeCount')
    .eq(0)
    .contains('li', companyEmployeeCount)
    .click()
  cy.getByCy('DropList.companyIndustry')
    .click()
    .getByCy('Dropdown.select.companyIndustry')
    .eq(0)
    .contains('li', companyIndustry)
    .click()
  cy.get('#segmentationNextStep').click()
}

Cypress.Commands.add('completeSegmentation', completeSegmentation)
