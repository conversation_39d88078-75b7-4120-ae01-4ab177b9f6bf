const createNewAccountViaAPI = (
  plansURL,
  planType,
  planID,
  segmentationDetails = {}
) => {
  //Registration process is done via API as it is not under the scope of this test
  cy.log(`Create a new Account in a ${planType} - ${planID} Plan (via API)`)
  cy.clearCookies()
  cy.visitEmailRegistrationPage(plansURL)

  cy.getCsrfToken().then(csrfToken => {
    cy.generateNewUser('low', `${planType}${planID}`).then(user => {
      cy.log('Send request with sign-up form data')
      cy.createCompanyViaAPI(user, planID, csrfToken)

      cy.log('Complete captcha challenge via API and return the csrfToken')
      cy.completeRecaptchaViaAPI(csrfToken)

      cy.log(
        'Send request to fill out the segmentation data and get the new companyId and segmentationId values'
      )

      cy.visit('/welcome/segmentation-questions/')
        .its('appData.marketing')
        .then(({ id, companyId }) => {
          const segmentationData = {
            segmentationID: id,
            companyID: companyId,
          }

          cy.completeSegmentationViaAPI(
            segmentationData,
            { ...segmentationDetails, companyName: user.company },
            csrfToken
          )
        })

      cy.visit('/welcome/personalizations-questions/')
        .url()
        .should('include', 'personalizations-questions')

      cy.log('Complete personalization questions via API')
      cy.completeSignupIntentionViaAPI(csrfToken)

      cy.log('Get mailbox slug after backend creates inbox')
      cy.window()
        .its('appData')
        .its('shared')
        .its('mailbox')
        .its('slug')
        .as('mailboxSlug')
      return cy.get('@mailboxSlug')
    })
  })
}

Cypress.Commands.add('createNewAccountViaAPI', createNewAccountViaAPI)
