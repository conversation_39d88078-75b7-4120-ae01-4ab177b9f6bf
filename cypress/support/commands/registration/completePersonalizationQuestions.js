const completePersonalizationQuestions = (options = {}) => {
  const {
    currentSolution = ['HELP_DESK'],
    currentHelpDesk = 'zendesk',
    signupChannels = ['EMAIL'],
  } = options

  cy.log(
    'Check the Personalization Questions screen from the Registration process'
  )
    .url()
    .should('include', '/welcome/personalizations-questions/')

  cy.get('h1').should('contain', 'Thanks! Last questions')

  cy.get('p').should('contain', 'Just a few more details to tailor your setup')

  cy.log('Complete personalization questions form')

  // Select current solution (Help Desk is typically pre-selected or we can select it)
  if (currentSolution.includes('HELP_DESK')) {
    cy.get('[data-testid*="solution"]').first().click()
  }

  // Select current help desk if Help Desk solution is selected
  if (currentHelpDesk) {
    cy.get('[data-testid*="helpdesk"]')
      .contains(currentHelpDesk, { matchCase: false })
      .click()
  }

  // Select signup channels
  if (signupChannels.includes('EMAIL')) {
    cy.get('[data-testid*="channel"]')
      .contains('Email', { matchCase: false })
      .click()
  }

  cy.log('Submit personalization questions form')
    .get('button')
    .contains('Complete Sign Up')
    .should('be.enabled')
    .click()
}

Cypress.Commands.add(
  'completePersonalizationQuestions',
  completePersonalizationQuestions
)
