// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

import { hasTags } from './utils'

// Alternatively you can use CommonJS syntax:
// require('./commands')
const { debuggerSupport } = require('cypress-debugger')
debuggerSupport()

// Global intercepts for setup-guide tagged tests
// beforeEach(() => {
// if (hasTags('setup-guide')) {
// Intercept external URLs to always return 200 for setup-guide tests
// cy.intercept('**/analytics.google.com/**', { statusCode: 200 })
// cy.intercept('**/www.google-analytics.com/**', { statusCode: 200 })
// cy.intercept('**/accounts.google.com/gsi/client/**', {
//   statusCode: 200,
// })
// cy.intercept('**/transcend-cdn.com/**', { statusCode: 200 })
// cy.intercept('**/grsm.io/**', { statusCode: 200 })
// cy.intercept('**/partnerlinks.io/**', { statusCode: 200, body: '' })
// cy.intercept('**/cta-service-cms2.hubspot.com/**', { statusCode: 200 })
// cy.intercept('**/px.ads.linkedin.com/**', { statusCode: 200 })
// cy.intercept('**/www.facebook.com/**', { statusCode: 200 })
// cy.intercept('**/api.hubapi.com/**', { statusCode: 200, body: {} })
// cy.intercept('**/www.redditstatic.com/**', { statusCode: 200 })
// cy.intercept('**/pixel-config.reddit.com/**', { statusCode: 200 })
// }
// })

// GLOBAL ERROR HANDLING

Cypress.on('uncaught:exception', err => {
  const headlessError = "Cannot set property 'status' of undefined"
  const guiError = 't.params is undefined'
  const indexError = "Cannot read properties of undefined (reading 'indexOf')"
  const indexError2 = "Cannot read property 'indexOf' of undefined"
  const lengthError = "Cannot read properties of undefined (reading 'length')"
  const lengthError2 = "Cannot read property 'length' of undefined"
  const destructure =
    "Cannot destructure property 'anchor' of 'range' as it is null"
  const resizeObserver = 'ResizeObserver'

  console.warn(err)

  // New Relic JS conflicts with monkey patching done by Cypress and causes this error
  if (
    err.message &&
    (err.message.startsWith(headlessError) ||
      err.message.startsWith(guiError) ||
      err.message.includes(indexError) ||
      err.message.includes(indexError2) ||
      err.message.includes(lengthError) ||
      err.message.includes(lengthError2) ||
      err.message.includes(destructure) ||
      err.message.includes(resizeObserver)) // Don't fail on ResizeObserver uncaught exception
  ) {
    return false
  }

  // Global error handling for beacon-api /customers endpoint returning 401
  if (
    err?.request?.url?.includes('beacon-api.local.hsenv.io.net/v1') &&
    err.request.url.includes('/customers')
  ) {
    return false
  }
})
