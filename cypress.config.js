const { defineConfig } = require('cypress')
const { cloudPlugin } = require('cypress-cloud/plugin')

module.exports = defineConfig({
  localBaseUrl: 'https://hs-app.local.hsenv.io',
  devBaseUrl: 'https://webdev-ci-secure.helpscout.net',
  prodBaseUrl: 'https://ci-secure-test.helpscout.net',
  timeDelayEnabled: false,
  timeDelay: 1000,
  chromeWebSecurity: false,
  videoUploadOnPasses: false,
  viewportHeight: 2000,
  viewportWidth: 1600,
  userAgent:
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36) (Cypress)',
  retries: {
    runMode: 1,
    openMode: 0,
  },
  defaultCommandTimeout: 20000,
  videoCompression: 0,
  urls: {
    chatApi: 'https://dchatapi.sumodev.net',
    beaconApi: 'https://dbeaconapi.sumodev.net',
    docsApi: 'https://dev-docsapi.helpscout.net',
    super: 'https://super.local.hsenv.io',
  },
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      const enhancedConfig = require('./cypress/plugins/index.js')(on, config)
      return cloudPlugin(on, enhancedConfig)
    },
    baseUrl: 'https://hs-app.local.hsenv.io',
    excludeSpecPattern: [
      '**/*.txt',
      '**/*.md',
      '**/*TODO*.js',
      '**/utils/**/*.spec.js',
    ],
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
    experimentalMemoryManagement: true,
  },
  blockHosts: ['*.google-analytics.com', '*.googletagmanager.com'],
})
